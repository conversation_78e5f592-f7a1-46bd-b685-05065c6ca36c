// 基准大小,iphone6的标准
const baseSize = 32
const dpr = window.devicePixelRatio
// 设置 rem 函数
const setRem = function () {
    // 当前页面宽度相对于 750 宽的缩放比例，可根据自己需要修改。
    // const maxWidth = 1440; // PC端最大宽度
    if (document.documentElement.clientWidth > 750) {
        document.documentElement.style.fontSize = '25px'
        return
    }
    const scale = document.documentElement.clientWidth / 750
    // 设置页面根节点字体大小
    // console.log(document.documentElement.clientWidth/10/(baseSize * Math.min(scale, 2)))
    document.documentElement.style.fontSize = baseSize * Math.min(scale, 2) + 'px'
}
// 初始化
setRem()
// 改变窗口大小时重新设置 rem
window.onresize = function () {
    setRem()
}
export default {
    setRem,
}
