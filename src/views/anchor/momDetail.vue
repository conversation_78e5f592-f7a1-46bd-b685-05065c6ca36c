<!-- 朋友圈详情 -->
<template>
    <!-- 页面骨架屏 -->
    <div class="app-container skeleton-container" v-if="loading">
        <!-- 头部骨架屏 -->
        <div class="skeleton-header">
            <div class="skeleton-header-top">
                <var-skeleton type="text" :loading="true" width="100%" height="20px" />
            </div>
            <div class="skeleton-header-content">
                <var-skeleton type="text" :loading="true" width="70%" height="16px" />
                <var-skeleton type="text" :loading="true" width="50%" height="14px" />
                <var-skeleton type="text" :loading="true" width="30%" height="12px" />
            </div>
        </div>

        <!-- 主内容骨架屏 -->
        <div class="skeleton-main">
            <div class="skeleton-image-large">
                <var-skeleton type="image" :loading="true" width="100%" height="300px" />
            </div>

            <!-- 评论列表骨架屏 -->
            <div class="skeleton-comment-list">
                <div class="skeleton-comment-item" v-for="i in 3" :key="i">
                    <div class="skeleton-comment-avatar">
                        <var-skeleton type="avatar" :loading="true" width="40px" height="40px" />
                    </div>
                    <div class="skeleton-comment-content">
                        <var-skeleton type="text" :loading="true" width="60%" height="14px" />
                        <var-skeleton type="text" :loading="true" width="100%" height="12px" />
                        <var-skeleton type="text" :loading="true" width="80%" height="12px" />
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部输入框骨架屏 -->
        <div class="skeleton-input">
            <div class="skeleton-input-field">
                <var-skeleton type="text" :loading="true" width="70%" height="40px" />
            </div>
            <div class="skeleton-input-button">
                <var-skeleton type="avatar" :loading="true" width="40px" height="40px" />
            </div>
        </div>
    </div>
    <!-- 实际页面内容 -->
    <div class="app-container" v-else>
        <div class="app-container-heder">
            <div class="moments-card-item-header">
                <div class="moments-card-item-header-left">
                    <div class="left-left-box">
                        <img src="@/assets/anchor/go-back.png" alt="" class="go-back-icon" @click="goBack">
                        <img :src="momDetail.avatar" alt="" class="moments-card-item-header-left-avatar">
                        <div class="is-online" v-if="momDetail.isOnline">

                        </div>
                    </div>
                    <div class="left-right-box">
                        <span class="name">{{ momDetail.name }}</span>
                        <div class="bradge-box">
                            <div class="age-box">
                                <img src="@/assets/home/<USER>" alt="" class="gril-icon">
                                <span class="age">{{ momDetail.age }}</span>
                            </div>
                            <img src="@/assets/home/<USER>" alt="" class="hot-icon" v-if="momDetail.isHot">
                        </div>
                    </div>
                </div>
                <div class="moments-card-item-header-right">
                    <div class="message">
                        <div class="message-box">
                            Messages
                        </div>
                    </div>
                    <img src="@/assets/anchor/more.png" alt="" class="more-infor-icon">
                </div>
            </div>
        </div>
        <div class="main-container">
            <div class="moments-card-item-content">
                <div class="moments-card-item-content-title">
                    <span class="title-text">{{ momDetail.title }}</span>
                </div>
                <div class="moments-card-item-content-img">
                    <div class="moments-card-item-content-img-one"
                        v-if="momDetail.moments && momDetail.moments.length === 1">
                        <img :src="momDetail.moments[0].img" alt="">
                    </div>
                    <div class="moments-card-item-content-img-repeat"
                        v-else-if="momDetail.moments && momDetail.moments.length > 1">
                        <div class="moments-card-item-content-img-repeat-item" v-for="i in momDetail.moments"
                            :key="i.id">
                            <img :src="i.img" alt="">
                        </div>
                    </div>
                </div>
            </div>
            <!---下面的点赞和评论以及日期-->
            <div class="moments-card-item-footer">
                <div class="moments-card-item-footer-left">
                    19/07
                </div>
                <div class="moments-card-item-footer-right">
                    <div class="moments-card-item-footer-right-box">
                        <img src="@/assets/common/like.png" alt="" class="like-icon">
                        <span class="like-text">19</span>
                    </div>
                    <div class="moments-card-item-footer-right-box">
                        <img src="@/assets/common/message-count.png" alt="" class="like-icon">
                        <span class="like-text">19</span>
                    </div>
                    <div class="moments-card-item-footer-right-box" @click="sayHello">
                        <img src="@/assets/common/say-hi.png" alt="" class="like-icon">
                        <span class="like-text say-hi-text">Say Hi ~</span>
                    </div>

                </div>
            </div>
            <!--Tip提示-->
            <div class="moments-tips">
                <img src="@/assets/anchor/tips.png" alt="" class="tips-icon">
                <span class="tips-text">Long press the comment to report or delete</span>
                <img src="@/assets/anchor/tips-close.png" alt="" class="tips-close-icon">
            </div>
            <!--下面是具体的提示-->
            <div class="comment-detail">
                <div class="comment-detail-header">
                    Commonts 2
                </div>
                <div class="comment-detail-content">
                    <div class="common-detail-content-item" v-for="i in comments" :key="i.id">
                        <div class="item-first" @click="replyComment(i)">
                            <div class="item-first-left">
                                <img :src="i.avatar" alt="" class="item-first-left-avatar" v-if="i.avatar">
                                <img src="@/assets/common/avatar-default.png" alt="" class="item-first-left-avatar"
                                    v-else>
                            </div>
                            <div class="item-first-right">
                                <span class="item-first-righ-name">{{ i.name }}</span>
                                <span class="item-first-right-text">{{ i.text }}</span>
                                <span class="item-first-right-time">{{ i.time }}</span>
                            </div>

                        </div>
                        <!---下面显示的child-->
                        <div class="item-child" v-if="i.children.length > 0">
                            <div v-if="i.children.length > 2">
                                <div class="item-first" v-for="j in i.children.slice(0, i.spliceIndex)" :key="j.id" @click="replyComment(j)">
                                    <div class="item-first-left">
                                        <img :src="j.avatar" alt="" class="item-first-left-avatar" v-if="j.avatar">
                                        <img src="@/assets/common/avatar-default.png" alt=""
                                            class="item-first-left-avatar" v-else>
                                    </div>
                                    <div class="item-first-right">
                                        <span class="item-first-right-name">{{ j.name }}</span>
                                        <span class="item-first-right-text">{{ j.text }}</span>
                                        <span class="item-first-right-time">{{ j.time }}</span>
                                    </div>
                                </div>
                                <div class="see-more-box" @click="seeMore(i)"
                                    v-if="i.spliceIndex && i.spliceIndex < i.children.length">
                                    <div class="see-more-box-line">

                                    </div>
                                    <span>View more replies</span>
                                </div>
                            </div>
                            <div v-else>
                                <div class="item-first" v-for="j in i.children" :key="j.id">
                                    <div class="item-first-left">
                                        <img :src="j.avatar" alt="" class="item-first-left-avatar" v-if="j.avatar">
                                        <img src="@/assets/common/avatar-default.png" alt=""
                                            class="item-first-left-avatar" v-else>
                                    </div>
                                    <div class="item-first-right">
                                        <span class="item-first-right-name">{{ j.name }}</span>
                                        <span class="item-first-right-text">{{ j.text }}</span>
                                        <span class="item-first-right-time">{{ j.time }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="input-box">
            <div class="input-box-left">
                <input type="text" class="input-box-right-input" :placeholder="placeholderText">
            </div>
            <div class="input-box-right">
                <img src="@/assets/common/send-message.png" alt="">
            </div>
        </div>
        <!--打招呼弹窗-->
        <sayHiPopup v-model:show="showSayHiPopup" @update:show="showSayHiPopup = $event" />
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import sayHiPopup from '@/components/sayHiPopup/sayHiPopup.vue'
import { useRouter } from 'vue-router'
const router = useRouter()
const goBack = () => {
    router.back()
}
const showSayHiPopup = ref(false)
const loading = ref(true)
const placeholderText = ref('Say something')
const comments = ref([
    {
        id: 1,
        name: 'Sarah 💎',
        avatar: 'https://picsum.photos/300/200?random=6',
        text: 'Amazing! Love this post so much! 🔥',
        time: '2 hours ago',
        children: []
    },
    {
        id: 2,
        name: 'Sarah 💎',
        avatar: 'https://picsum.photos/300/200?random=5',
        text: 'Amazing! Love this post so much! 🔥',
        time: '2 hours ago',
        children: [
            {
                id: 3,
                name: 'Sarah 💎',
                avatar: 'https://picsum.photos/300/200?random=4',
                text: 'Amazing! Love this post so much! 🔥',
                time: '2 hours ago'
            }
        ]
    },
    {
        id: 3,
        name: 'Mike 🚀',
        avatar: 'https://picsum.photos/300/200?random=2',
        text: 'You look stunning! Keep it up! ✨',
        time: '1 hour ago',
        spliceIndex: 2,
        children: [
            {
                id: 3,
                name: 'Sarah 💎',
                avatar: 'https://picsum.photos/300/200?random=1',
                text: 'Amazing! Love this post so much! 🔥',
                time: '2 hours ago'
            },
            {
                id: 4,
                name: 'Sarah 💎',
                avatar: 'https://picsum.photos/300/200?random=2',
                text: 'Amazing! Love this post so much! 🔥',
                time: '2 hours ago'
            },
            {
                id: 5,
                name: 'Sarah 💎',
                avatar: 'https://picsum.photos/300/200?random=3',
                text: 'Amazing! Love this post so much! 🔥',
                time: '2 hours ago'
            }
        ]
    }
])

const sayHello = () => {
    showSayHiPopup.value = true
}
const replyComment = (item: any) => {
    placeholderText.value = `@${item.name} `
}
// 模拟加载数据
onMounted(() => {
    setTimeout(() => {
        loading.value = false
    }, 2000)
})
const momDetail = ref({
    id: 1,
    avatar: 'https://picsum.photos/300/200?random=2',
    name: 'SAYRA💎',
    title: 'Are you a good driver on curves?!🔥☁️',
    isFollow: true,
    age: 32,
    isOnline: true,
    isHot: true,
    moments: [
        {
            id: 1,
            img: 'https://ilivegirl.s3.amazonaws.com/main/79ebc489b875357c6ae8dfee20b7836120250727164040-medium.jpg',
            content: 'Bad girls club ✨',
        },
        {
            id: 2,
            img: 'https://ilivegirl.s3.amazonaws.com/main/79ebc489b875357c6ae8dfee20b7836120250727164040-medium.jpg',
            content: 'Bad girls club ✨',
        },

    ]
})

const seeMore = (item: any) => {
    item.spliceIndex = item.children.length
}
</script>
<style lang="scss" scoped>
.app-container {
    overflow-y: hidden;
    font-family: var(--font-family-urbanist);

    // 骨架屏样式
    &.skeleton-container {
        background: #f5f5f5;
        padding: 0;
        overflow: hidden;

        .skeleton-header {
            background: #000;
            padding: 15px;
            margin-bottom: 0;

            .skeleton-header-top {
                margin-bottom: 10px;
            }

            .skeleton-header-content {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }
        }

        .skeleton-main {
            background: #1a1a2e;
            padding: 15px;
            min-height: calc(100vh - 200px);

            .skeleton-image-large {
                margin-bottom: 20px;
                border-radius: 12px;
                overflow: hidden;
            }

            .skeleton-comment-list {
                .skeleton-comment-item {
                    display: flex;
                    gap: 12px;
                    margin-bottom: 20px;
                    padding: 10px 0;

                    .skeleton-comment-avatar {
                        flex-shrink: 0;
                    }

                    .skeleton-comment-content {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        gap: 8px;
                    }
                }
            }
        }

        .skeleton-input {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: #1a1a2e;
            padding: 15px;
            display: flex;
            align-items: center;
            gap: 10px;

            .skeleton-input-field {
                flex: 1;
                border-radius: 20px;
                overflow: hidden;
            }

            .skeleton-input-button {
                flex-shrink: 0;
                border-radius: 50%;
                overflow: hidden;
            }
        }
    }

    .app-container-heder {
        width: 100%;
        height: 44px;
        padding: 0 15px;
        background-color: #000;
        box-sizing: border-box;

        .moments-card-item-header {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .moments-card-item-header-left {
                display: flex;
                align-items: center;
                gap: 8px;

                .left-left-box {
                    display: flex;
                    align-items: center;
                    position: relative;

                    .go-back-icon {
                        width: 30px;
                        height: 30px;
                        margin-right: 3px;
                    }

                    .moments-card-item-header-left-avatar {
                        width: 36px;
                        height: 36px;
                        border-radius: 50%;
                        object-fit: cover;
                    }

                    .is-online {
                        position: absolute;
                        right: 2px;
                        bottom: 2px;
                        width: 6px;
                        height: 6px;
                        border-radius: 50%;
                        background-color: $success-color;
                    }
                }

                .left-right-box {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;

                    .name {
                        font-size: 16px;
                        font-weight: 700;
                        color: #fff;
                        line-height: 19px;
                    }

                    .bradge-box {
                        margin-top: 3px;
                        display: flex;
                        align-items: center;
                        gap: 4px;


                        .age-box {
                            display: flex;
                            align-items: center;
                            gap: 1px;
                            width: max-content;
                            background-color: $common-color;
                            padding: 1px 4px;
                            border-radius: 6px;

                            .gril-icon {
                                width: 10px;
                                height: 10px;
                            }

                            .age {
                                font-size: 10px;
                                font-weight: 800;
                                color: #fff;
                                line-height: 12px;
                            }
                        }

                        .hot-icon {
                            height: 12px;
                        }
                    }
                }
            }

            .moments-card-item-header-right {
                display: flex;
                align-items: center;
                gap: 10px;

                .message {
                    padding: 1px;
                    background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                    border-radius: 30px;
                    box-sizing: border-box;

                    .message-box {
                        padding: 5.5px 6.5px;
                        background: linear-gradient(90deg, #341511 0%, #340527 100%);

                        border-radius: 29px;
                        font-size: 14px;
                        font-weight: 700;
                        line-height: 17px;
                        color: #fff;
                    }
                }

                .more-infor-icon {
                    width: 30px;
                    height: 30px;
                }
            }
        }
    }

    .main-container {
        flex: 1;
        padding-bottom: 100px;
        box-sizing: border-box;
        overflow: auto;

        .moments-card-item-content {
            margin-top: 10px;
            padding: 0 15px;
            box-sizing: border-box;

            .moments-card-item-content-title {
                .title-text {
                    font-size: 15px;
                    font-weight: 700;
                    color: #fff;
                    line-height: 18px;
                }
            }

            .moments-card-item-content-img {
                margin-top: 12px;

                .moments-card-item-content-img-one {
                    img {
                        width: 180px;
                        max-height: 255px;
                        min-height: 113px;
                        object-fit: cover;
                    }
                }

                .moments-card-item-content-img-repeat {
                    display: grid;
                    grid-template-columns: repeat(3, 1fr);
                    gap: 10px;

                    .moments-card-item-content-img-repeat-item {
                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                        }
                    }
                }
            }
        }

        .moments-card-item-footer {
            margin-top: 20px;
            padding: 0 15px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .moments-card-item-footer-left {
                font-size: 12px;
                font-weight: 600;
                color: rgba($color: #fff, $alpha: 0.5);
                line-height: 14px;
            }

            .moments-card-item-footer-right {
                display: flex;
                align-items: center;
                gap: 20px;

                .moments-card-item-footer-right-box {
                    display: flex;
                    align-items: center;
                    gap: 4px;

                    .like-icon {
                        width: 18px;
                        height: 18px;
                    }

                    .like-text {
                        font-size: 12px;
                        font-weight: 600;
                        color: rgba($color: #fff, $alpha: 0.5);
                        line-height: 14px;
                    }

                    .say-hi-text {
                        color: $common-color;
                    }
                }
            }
        }

        .moments-tips {
            margin-top: 20px;
            padding: 3px 15px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            gap: 6px;
            position: relative;
            background: linear-gradient(90deg, rgba(255, 129, 43, 0.1) 0%, rgba(255, 11, 194, 0.1) 100%);

            .tips-icon {
                width: 30px;
                height: 30px;
            }

            .tips-text {
                font-family: var(--font-family-urbanist);
                font-weight: 600;
                font-size: 12px;
                color: #fff;
            }

            .tips-close-icon {
                width: 8px;
                height: 8px;
                position: absolute;
                right: 15px;
            }
        }

        .comment-detail {
            margin-top: 20px;
            padding: 0 15px;

            &-header {
                font-size: 14px;
                color: #fff;
                font-weight: 500;
                line-height: 17px;
            }

            &-content {
                .common-detail-content-item {

                    .item-first {
                        margin-top: 20px;
                        display: flex;
                        gap: 10px;

                        .item-first-left {
                            width: 46px;
                            height: 46px;
                            flex-shrink: 0;

                            img {
                                width: 100%;
                                height: 100%;
                                object-fit: cover;
                                border-radius: 50%;
                            }
                        }

                        .item-first-right {
                            display: flex;
                            flex-direction: column;

                            .item-first-righ-name {
                                font-size: 14px;
                                font-weight: 500;
                                color: rgba($color: #fff, $alpha: 0.7);
                                line-height: 17px;
                            }

                            .item-first-right-text {
                                margin-top: 7px;
                                font-size: 15px;
                                font-weight: 700;
                                color: #fff;
                                line-height: 18px;
                            }

                            .item-first-right-time {
                                margin-top: 12px;
                                font-size: 12px;
                                font-weight: 500;
                                color: rgba($color: #fff, $alpha: 0.5);
                                line-height: 14px;
                            }
                        }
                    }

                    .item-child {
                        margin-top: 20px;
                        padding-inline-start: 46px;

                        .see-more-box {
                            margin-top: 12px;
                            font-size: 14px;
                            font-weight: 500;
                            color: rgba($color: #fff, $alpha: 0.5);
                            line-height: 14px;
                            display: flex;
                            align-items: center;
                            gap: 4px;

                            .see-more-box-line {
                                width: 40px;
                                height: 1px;
                                background-color: rgba($color: #fff, $alpha: 0.5);
                            }
                        }
                    }

                }
            }
        }
    }

    .input-box {
        position: fixed;
        bottom: calc(env(safe-area-inset-bottom));
        left: 0;
        width: 100%;
        height: 63px;
        background: #010101;
        backdrop-filter: blur(5px);
        display: flex;
        align-items: center;
        padding: 8px 15px 15px;
        gap:20px;
        box-sizing: border-box;

        .input-box-left {
            flex: 1;
            height: 100%;
            background-color: #1E1D38;
            border-radius: 20px;
            padding-inline-start: 34px;
            box-sizing: border-box;
            .input-box-right-input {
                width: 100%;
                height: 100%;
                outline: none;
                border:none;
                background-color: transparent;
                font-size: 14px;
                font-weight: 500;
                color: #fff;
                font-family: var(--font-family-urbanist);
            }
        }

        .input-box-right {
            width: 60px;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #1E1D38;
            border-radius: 30px;
            img {
                width: 40px;
                height: 40px;
            }
        }
    }
}
</style>