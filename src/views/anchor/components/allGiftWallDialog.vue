<template>

    <var-popup position="left" v-model:show="showAllGiftWallDialog" :close-on-click-overlay="false" :safe-area="true"
        :default-style="false">
        <div class="all-gift-wall">

        </div>
    </var-popup>

</template>

<script lang="ts">
export default {
    name: 'allGiftWallDialog',
    props: {
        show: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {

            showAllGiftWallDialog: this.show
        };
    },
    watch: {
        show: {
            handler(newVal) {
                this.showAllGiftWallDialog = newVal
            },
            immediate: true
        }
    },
    mounted() {

    },
    methods: {
        close() {
            this.showAllGiftWallDialog = false
            this.$emit('close')
            this.$emit('update:show', false)
        },
    }
}
</script>
<style lang='scss' scoped>
.gift-wall-dialog {
    width: 100%;
    height: 100%;
    font-family: var(--font-family-urbanist);
    background-color: red;
}
</style>