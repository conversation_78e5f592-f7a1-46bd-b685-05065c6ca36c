<!-- 选择性别弹窗 -->
<template>
    <div>
        <var-popup position="center" v-model:show="showHonorMedalsDialog" @click-overlay="close" :safe-area="true"
            :default-style="false">
            <div class="honor-medals-dialog">
                <div class="honor-medals-dialog-box">
                    <div class="honor-medals-dialog-box-img">
                        <img :src="honorMedalsData.img" alt="" class="honor-medals-dialog-box-img-item">
                    </div>
                    <div class="honor-medals-dialog-box-name">
                        {{ honorMedalsData.name }}
                    </div>
                    <div class="honor-medals-dialog-box-desc">
                        {{ honorMedalsData.desc }}
                    </div>
                    <div class="honor-medals-dialog-box-time">
                        Obtained time:{{ honorMedalsData.time }}
                        <br>
                        <span>Validity:permanent</span>
                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts">
export default {
    name: 'honorMedalsDialog',
    props: {
        show: {
            type: Boolean,
            default: false
        },
        honorMedalsData: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data() {
        return {

            showHonorMedalsDialog: this.show
        };
    },
    watch: {
        show: {
            handler(newVal) {
                this.showHonorMedalsDialog = newVal
            },
            immediate: true
        }
    },
    mounted() {

    },
    methods: {
        close() {
            this.showHonorMedalsDialog = false
            this.$emit('close')
            this.$emit('update:show', false)
        },
    }
}
</script>
<style lang='scss' scoped>
.honor-medals-dialog {
    font-family: var(--font-family-urbanist);

    .honor-medals-dialog-box {
        width: 315px;
        padding: 30px;
        box-sizing: border-box;
        background: url('@/assets/common/gift-bg.png') no-repeat center center;
        background-size: 100% 100%;
        border-radius: 20px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .honor-medals-dialog-box-img {
            width: 100%;
            height: 110px;
            background: #13103B;
            border: 1px solid #2F3261;
            border-radius: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .honor-medals-dialog-box-name{
            margin-top: 20px;
            font-size: 18px;
            font-weight: 700;
            line-height: 22px;
            color: #fff;
        }
        .honor-medals-dialog-box-desc{
            margin-top: 10px;
            font-size: 14px;
            font-weight: 500;
            line-height: 17px;
            color: #FFFFFFB2;
        }
        .honor-medals-dialog-box-time{
            margin-top: 40px;
            font-size: 12px;
            font-weight: 600;
            line-height: 14px;
            color: #FFFFFF80;
            text-align: center;
        }
    }
}
</style>