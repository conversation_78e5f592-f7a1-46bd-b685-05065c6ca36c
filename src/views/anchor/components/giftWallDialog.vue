<!-- 选择性别弹窗 -->
<template>
    <div>
        <var-popup position="center" v-model:show="showGiftWallDialog" @click-overlay="close" :safe-area="true"
            :default-style="false">
            <div class="gift-wall-dialog">
                <div class="gift-wall-dialog-box">
                    <img :src="giftWallData.img" alt="" class="gift-wall-dialog-box-img">
                    <div class="gift-wall-dialog-box-name">
                        {{ giftWallData.name }}
                    </div>
                    <div class="gift-wall-dialog-box-price">
                        <span>Gift Price:</span>
                        <img src="@/assets/common/coins.png" alt="">
                        <span>{{ giftWallData.price }}</span>
                    </div>
                    <div class="gift-wall-dialog-box-price">
                        <span>Lighting attempts:</span>
                        <span>{{ giftWallData.attempts }}</span>
                    </div>
                    <!--时间-->
                    <div class="gift-wall-dialog-box-time">
                        First Light- Up:{{ giftWallData.time }}
                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts">
export default {
    name: 'giftWallDialog',
    props: {
        show: {
            type: Boolean,
            default: false
        },
        giftWallData: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data() {
        return {

            showGiftWallDialog: this.show
        };
    },
    watch: {
        show: {
            handler(newVal) {
                this.showGiftWallDialog = newVal
            },
            immediate: true
        }
    },
    mounted() {

    },
    methods: {
        close() {
            this.showGiftWallDialog = false
            this.$emit('close')
            this.$emit('update:show', false)
        },
    }
}
</script>
<style lang='scss' scoped>
.gift-wall-dialog {
    font-family: var(--font-family-urbanist);

    .gift-wall-dialog-box {
        width: 315px;
        padding: 30px 30px 44px;
        box-sizing: border-box;
        background: url('@/assets/common/gift-bg.png') no-repeat center center;
        background-size: 100% 100%;
        border-radius: 20px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .gift-wall-dialog-box-img {
            width: 100px;
            height: 100px;
        }

        .gift-wall-dialog-box-name {
            margin: 20px 0;
            font-size: 18px;
            font-weight: 700;
            line-height: 22px;
            color: #fff;

        }

        .gift-wall-dialog-box-price {
            width: 100%;
            height: 44px;
            margin-bottom: 8px;
            background: rgba($color: #fff, $alpha: .1);
            border-radius: 30px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            padding-inline-start: 20px;
            box-sizing: border-box;

            span {
                font-size: 14px;
                font-weight: 500;
                line-height: 17px;
                color: #fff;
            }

            img {
                margin: 0 4px;
                width: 20px;
                height: 20px;
            }
        }
        .gift-wall-dialog-box-time{
            margin-top: 20px;
            font-size: 12px;
            font-weight: 600;
            line-height: 14px;
            color: #FFFFFF80;
        }
    }
}
</style>