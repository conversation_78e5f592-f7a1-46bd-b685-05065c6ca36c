<!-- app-container -->
<template>
    <!-- 页面骨架屏 -->
    <div class="app-container skeleton-container" v-if="skeletonLoading">
        <!-- 头部骨架屏 -->
        <div class="skeleton-header">
            <var-skeleton type="text" :loading="true" width="100%" height="44px" />
        </div>
        
        <!-- 轮播骨架屏 -->
        <div class="skeleton-swiper">
            <var-skeleton type="image" :loading="true" width="100%" height="530px" />
        </div>
        
        <!-- 主播信息骨架屏 -->
        <div class="skeleton-anchor-info">
            <div class="skeleton-anchor-header">
                <div class="skeleton-anchor-left">
                    <var-skeleton type="text" :loading="true" width="60%" height="20px" />
                    <var-skeleton type="text" :loading="true" width="40%" height="12px" />
                    <var-skeleton type="text" :loading="true" width="50%" height="12px" />
                </div>
                <div class="skeleton-anchor-right">
                    <var-skeleton type="text" :loading="true" width="80px" height="32px" />
                </div>
            </div>
            
            <div class="skeleton-anchor-desc">
                <var-skeleton type="text" :loading="true" width="100%" height="14px" />
                <var-skeleton type="text" :loading="true" width="90%" height="14px" />
            </div>
            
            <div class="skeleton-like-box">
                <var-skeleton type="text" :loading="true" width="100%" height="60px" />
            </div>
            
            <div class="skeleton-tags">
                <var-skeleton type="text" :loading="true" width="30%" height="24px" />
                <var-skeleton type="text" :loading="true" width="25%" height="24px" />
                <var-skeleton type="text" :loading="true" width="35%" height="24px" />
            </div>
        </div>
        
        <!-- 标签页骨架屏 -->
        <div class="skeleton-tabs">
            <div class="skeleton-tab-header">
                <var-skeleton type="text" :loading="true" width="60px" height="22px" />
                <var-skeleton type="text" :loading="true" width="60px" height="22px" />
            </div>
            
            <div class="skeleton-tab-content">
                <div class="skeleton-moments-item" v-for="i in 3" :key="i">
                    <div class="skeleton-moments-header">
                        <var-skeleton type="avatar" :loading="true" width="36px" height="36px" />
                        <div class="skeleton-moments-info">
                            <var-skeleton type="text" :loading="true" width="80px" height="16px" />
                            <var-skeleton type="text" :loading="true" width="60px" height="12px" />
                        </div>
                    </div>
                    <div class="skeleton-moments-content">
                        <var-skeleton type="text" :loading="true" width="100%" height="16px" />
                        <var-skeleton type="image" :loading="true" width="100%" height="200px" />
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 底部操作骨架屏 -->
        <div class="skeleton-bottom-operation">
            <var-skeleton type="text" :loading="true" width="70px" height="44px" />
            <var-skeleton type="text" :loading="true" width="70px" height="44px" />
            <var-skeleton type="text" :loading="true" width="100%" height="44px" />
        </div>
    </div>

    <!-- 实际页面内容 -->
    <div class="app-container" @scroll="handleScroll" v-else>
        <var-list loading-text="" finished-text="" error-text="" :finished="finished" v-model:loading="loading"
            @load="loadMoreData(0)">
            <template #loading>
            </template>
            <!-- 顶部导航栏 -->
            <div class="app-container-header" :style="{ 'background-color': `rgba(0,0,0,${opacity})` }">
                <img src="@/assets/anchor/go-back.png" alt="" class="left-icon" @click="goBack">
                <div class="app-container-header-status">
                    <img src="@/assets/anchor/live.png" alt="" v-if="anchorStatus == 2">
                    <img src="@/assets/anchor/premium.png" alt="" v-else-if="anchorStatus == 3">
                </div>
                <img src="@/assets/anchor/more.png" alt="" class="right-icon" @click="showReportAnchorDialog = true">
            </div>

            <!-- 轮播容器 -->
            <div class="anchor-detail">
                <swiper :slides-per-view="1" :space-between="0" :loop="false" :autoplay="{
                    delay: 5000,
                    disableOnInteraction: false,
                }" :pagination="paginationConfig" :modules="modules" @slide-change="onSlideChange"
                    @autoplay-time-left="onAutoplayTimeLeft" class="story-swiper" ref="swiperRef">
                    <swiper-slide v-for="(image, index) in swiperList" :key="index" class="story-slide">
                        <div class="image-container">
                            <img :src="image" :alt="`Story ${index + 1}`" class="story-image" />
                        </div>
                    </swiper-slide>
                    <!-- 底部导航 -->
                    <div class="bottom-nav">
                        <!-- 自定义分页器 -->
                        <div class="swiper-pagination"></div>
                        <!-- 图片计数器 -->
                        <div class="slide-counter">{{ currentSlide + 1 }}/{{ swiperList.length }}</div>

                    </div>
                </swiper>
            </div>
            <!--下面是主播的详情-->
            <div class="anchor-content">
                <!--展示主播名字，标签以及需要金币-->
                <div class="content-header">
                    <div class="content-header-left">
                        <!--展示主播名字，标签以及需要金币-->
                        <div class="left-top">
                            <span class="left-top-name">🙋Baby sefeti🙋</span>
                            <div class="left-top-age">
                                <img src="@/assets/home/<USER>" alt="">
                                <span>18</span>
                            </div>
                        </div>
                        <!---主播的状态-->
                        <div class="left-section">
                            <div class="icon-online">
                                <span class="icon-text">online</span>
                            </div>
                            <div class="icon-hot">
                                <img src="@/assets/anchor/hot.webp" alt="" class="icon-img">
                                <span class="icon-text">Hot</span>
                            </div>
                            <div class="icon-verified">
                                <img src="@/assets/anchor/verified.png" alt="" class="icon-img">
                                <span class="icon-text">Verified</span>
                            </div>
                        </div>
                        <!--主播的ID 以及关注数-->
                        <div class="left-bottom">
                            <span class="left-section-bottom-id">ID: 1234567890</span>
                            <span class="left-section-bottom-follow">Followers 1.55k</span>
                        </div>
                    </div>
                    <div class="content-header-right">
                        <img src="@/assets/common/coins.png" alt="">
                        <span>20 coins/min</span>
                    </div>
                </div>
                <!---主播的简介-->
                <div class="content-desc">
                    Dreamer. Doer. Achiever. 🌟 Living life one adventure at a time. 📍 Wanderlust soul. 💬 Let’s
                    connect
                    and create memories!
                </div>
                <!--喜欢主播的百分比-->
                <div class="content-like">
                    <div class="content-like-box">
                        <div class="content-like-box-left">
                            <img src="@/assets/anchor/more-like.png" alt="" class="content-like-box-left-img">
                            <div class="content-like-box-left-text">
                                <span class="content-like-box-left-text-num">99%</span>
                                <span class="content-like-box-left-text-rate">Like Rate</span>
                            </div>
                        </div>
                        <!--展示具体的数量-->
                        <div class="content-like-box-right">
                            <div class="right-top">
                                <img src="@/assets/anchor/like.png" alt="" class="right-top-img">
                                <img src="@/assets/anchor/not-like.png" alt="" class="right-top-img">
                            </div>
                            <div class="right-section">

                            </div>
                            <div class="right-bottom">
                                <span class="right-bottom-like">321</span>
                                <span class="right-bottom-nolike">31</span>
                            </div>
                        </div>
                    </div>
                </div>
                <!---主播的标签-->
                <div class="content-tag">
                    <div class="content-tag-box">
                        <div class="content-tag-box-item item-1">
                            Pretty Look ×26
                        </div>
                        <div class="content-tag-box-item item-2">
                            Satisfying ×16
                        </div>
                        <div class="content-tag-box-item item-3">
                            Nice Figure ×16
                        </div>
                        <div class="content-tag-box-item item-4">
                            Good Attitude ×26
                        </div>
                        <div class="content-tag-box-item item-5">
                            Good Attitude ×26
                        </div>
                        <div class="content-tag-box-item item-6">
                            Talkative ×26
                        </div>
                    </div>
                </div>
            </div>

            <!---下面的分类-->
            <div class="tab-wrap">
                <div class="tab-wrap-header">
                    <div class="tab-wrap-header-item" :class="{ 'tab-active': activeTab === 0 }"
                        @click="changeActiveTab(0)">
                        Moments
                    </div>
                    <div class="tab-wrap-header-item" :class="{ 'tab-active': activeTab === 1 }"
                        @click="changeActiveTab(1)">
                        Honor
                    </div>
                </div>

                <div class="tab-content" v-show="activeTab === 0">
                    <MomentsCard :dataList="dataList" @sayHello="sayHello" />
                </div>
                <div class="tab-content" v-show="activeTab === 1">
                    <div class="honor-content">
                        <div class="honor-content-item">
                            <div class="honor-content-item-left">
                                Gift wall
                            </div>
                            <div class="honor-content-item-right">
                                <span>{{ giftNum }}</span>
                                <img src="@/assets/anchor/arrow-right.png" alt="">
                            </div>
                        </div>
                        <!--下面是礼物展示部分-->
                        <div class="honor-content-list">
                            <div class="honor-content-list-item" v-for="item in 10" :key="item">
                                <img src="@/assets/test/gift.gif" alt="">
                            </div>
                        </div>
                        <div class="honor-content-item">
                            <div class="honor-content-item-left">
                                Honor Medals
                            </div>
                            <div class="honor-content-item-right">
                                <span>{{ medalsNum }}</span>
                                <img src="@/assets/anchor/arrow-right.png" alt="">
                            </div>
                        </div>
                        <!--下面是medal展示-->
                        <div class="honor-medal-list">
                            <div class="honor-medal-list-item" v-for="item in 10" :key="item">
                                <img src="@/assets/test/medal.png" alt="">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </var-list>
        <!--返回顶部-->
        <div class="up-match-wrap" ref="upMatchWrap" @click="handleUpMatch">
            <div class="up-match-btn">
                <img src="@/assets/common/up-to.png" alt="" class="up-match-btn-img">
            </div>
        </div>
        <!---下面固定的操作部分-->
        <div class="bottom-operation">
            <div class="follow">
                <div class="follow-box">
                    <img src="@/assets/anchor/add-follow.png" alt=""></img>
                </div>

            </div>
            <div class="follow">
                <div class="follow-box">
                    <img src="@/assets/anchor/message.png" alt="">
                </div>
            </div>
            <div class="btn-call-wrap-vir" v-if="anchorStatus == 1">
                <div class="btn-call-wrap animate-wave">
                    <div class="w w1"></div>
                    <div class="w w2"></div>
                    <div class="btn-call-wrap-button">
                        <div class="btn-call-wrap-button-icon">
                            <img src="@/assets/anchor/video-call.png" alt="" class="btn-call-wrap-button-icon-img">
                        </div>
                        <div class="btn-call-wrap-button-text">
                            Call&nbsp;Now
                        </div>
                    </div>
                </div>
            </div>
            <!--live-->
            <div class="btn-call-wrap-vir" v-else-if="anchorStatus == 2">
                <div class="btn-call-wrap">
                    <div class="live-call-wrap-button">
                        <img src="@/assets/anchor/live.png" alt="" class="live-call-wrap-button-img">
                        <div class="live-call-wrap-button-text">
                            Live&nbsp;Now
                        </div>
                    </div>
                </div>
            </div>
            <!--premium-->
            <div class="btn-call-wrap-vir" v-else-if="anchorStatus == 3">
                <div class="btn-call-wrap">
                    <div class="premium-call-wrap-button">
                        <img src="@/assets/anchor/live.png" alt="" class="premium-call-wrap-button-img">
                        <div class="premium-call-wrap-button-text">
                            Premium
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <sayHiPopup v-model:show="showSayHiPopup" @update:show="showSayHiPopup = $event" />
        <reportAnchorDialog v-model:show="showReportAnchorDialog" @update:show="showReportAnchorDialog = $event" />
        <rateDialog v-model:show="showRateDialog" @update:show="showRateDialog = $event" />
        <!--礼物弹窗-->
        <giftWallDialog v-model:show="showGiftWallDialog" :giftWallData="giftWallData" @update:show="showGiftWallDialog = $event" />
        <!--medal 详情-->
        <honorMedalsDialog v-model:show="showHonorMedalsDialog" :honorMedalsData="honorMedalsData" @update:show="showHonorMedalsDialog = $event" />
        <!--全部的gift 礼物详情-->
        <allGiftWallDialog v-model:show="showAllGiftWallDialog" @update:show="showAllGiftWallDialog = $event" />
        <!--主播连线-->
        <anchorCall v-model:show="showAnchorCall" @update:show="showAnchorCall = $event" />
    </div>

</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

import img1 from '@/assets/test/1.jpg'
import img2 from '@/assets/test/2.jpg'
import img3 from '@/assets/test/3.jpg'
import img4 from '@/assets/test/4.jpg'
import img5 from '@/assets/test/5.jpg'
import img6 from '@/assets/test/6.jpg'
import img7 from '@/assets/test/7.jpg'
import giftImg from '@/assets/test/gift.gif'
import medalImg from '@/assets/test/medal.png'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Autoplay, Pagination } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/autoplay'
import 'swiper/css/pagination'
import MomentsCard from '../moments/components/momentsCard.vue'
import sayHiPopup from '@/components/sayHiPopup/sayHiPopup.vue'
import reportAnchorDialog from './components/reportAnchor.vue'
import rateDialog from '@/components/rateDialog/rateDialog.vue'

import giftWallDialog from './components/giftWallDialog.vue'
import honorMedalsDialog from './components/honorMedalsDaialog.vue'
import anchorCall from '@/components/anchorCall/index.vue'

import allGiftWallDialog from './components/allGiftWallDialog.vue'


const router = useRouter()
// 响应式数据
const swiperList = ref([img1, img2, img3, img4, img5, img6, img7])
const currentSlide = ref(0)
const isLiked = ref(false)
const swiperRef = ref<any>(null)
const progressWidth = ref(0)
const modules = [Autoplay, Pagination]
const isScroll = ref(false)
const upMatchWrap = ref<HTMLElement | null>(null)
const showSayHiPopup = ref(false)
const opacity = ref(0)

const showAnchorCall = ref(true) //主播连线

const activeTab = ref(0)
const loading = ref(false)
const finished = ref(false)

const dataList = ref<any[]>([])
const anchorStatus = ref(3)

const giftNum = ref(20)
const medalsNum = ref(20)

const showReportAnchorDialog = ref(false)
const showRateDialog = ref(false)
const showGiftWallDialog = ref(false)
const giftWallData = ref({
   img:giftImg,
   name:'Apex Race',
   price:3699,
   attempts:3,
   time:'01/01/2017'
})
const showHonorMedalsDialog = ref(false)
const honorMedalsData = ref({
    img:medalImg,
    name:'7th Chat Star',
    desc:'You are the 7th chat star of the day!',
    time:'22/08/2024'
})

const showAllGiftWallDialog = ref(false)
const skeletonLoading = ref(true)
/**
 * @description: 加载更多数据
 * @param {number} type? 0:anchor 1: moments
 * @return {void}
 */  
const loadMoreData = (type?: number) => {
    if (activeTab.value === 0) {
        console.log(11111)
        setTimeout(() => {
            dataList.value = [
                {
                    id: 1,
                    avatar: 'https://picsum.photos/300/200?random=2',
                    name: 'SAYRA💎',
                    title: 'Are you a good driver on curves?!🔥☁️',
                    isFollow: true,
                    age: 32,
                    isOnline: true,
                    isHot: true,
                    moments: [
                        {
                            id: 1,
                            img: 'https://ilivegirl.s3.amazonaws.com/main/79ebc489b875357c6ae8dfee20b7836120250727164040-medium.jpg',
                            content: 'Bad girls club ✨',
                        },
                        {
                            id: 2,
                            img: 'https://ilivegirl.s3.amazonaws.com/main/79ebc489b875357c6ae8dfee20b7836120250727164040-medium.jpg',
                            content: 'Bad girls club ✨',
                        },

                    ]
                },
                {
                    id: 2,
                    avatar: 'https://picsum.photos/300/200?random=3',
                    name: 'SAYRA💎',
                    title: 'Are you a good driver on curves?!🔥☁️',
                    isFollow: false,
                    age: 32,
                    isOnline: true,
                    moments: [
                        {
                            id: 1,
                            img: 'https://ilivegirl.s3.amazonaws.com/main/79ebc489b875357c6ae8dfee20b7836120250727164040-medium.jpg',
                            content: 'Bad girls club ✨',
                        },
                    ]
                },
            ]
            loading.value = false
            finished.value = true
        }, 1000)
    } else {
        loading.value = false
        finished.value = true
    }

}
// 分页器配置
const paginationConfig = {
    el: '.swiper-pagination',
    clickable: true,
    renderBullet: function (index: number, className: string) {
        return `<div class="${className} custom-bullet"><img src="${swiperList.value[index]}" alt="thumbnail" /></div>`
    }
}

// 方法
const goBack = () => {
    console.log('返回')
    router.back()
    // 这里可以添加路由跳转逻辑
}

const showMore = () => {
    console.log('更多选项')
    // 这里可以添加更多选项的逻辑
}

const onSlideChange = (swiper: any) => {
    currentSlide.value = swiper.activeIndex
    progressWidth.value = 0

    // 使用更安全的方法自动滚动分页器
    setTimeout(() => {
        const paginationEl = document.querySelector('.swiper-pagination') as HTMLElement
        const activeBullet = paginationEl?.querySelector('.swiper-pagination-bullet-active') as HTMLElement
        if (paginationEl && activeBullet) {
            const containerRect = paginationEl.getBoundingClientRect()
            const bulletRect = activeBullet.getBoundingClientRect()
            const scrollLeft = bulletRect.left - containerRect.left - (containerRect.width / 2) + (bulletRect.width / 2)
            paginationEl.scrollLeft = scrollLeft
        }
    }, 50)
}

const onAutoplayTimeLeft = (_swiper: any, _time: number, progress: number) => {
    progressWidth.value = (1 - progress) * 100
}

const getProgressWidth = (index: number) => {
    if (index < currentSlide.value) {
        return '100%'
    } else if (index === currentSlide.value) {
        return `${progressWidth.value}%`
    } else {
        return '0%'
    }
}
const handleUpMatch = () => {
    const scrollDom = document.querySelector('.app-container') as HTMLElement
    scrollDom.scrollTo({
        top: 0,
        behavior: 'smooth'
    })
}
const handleScroll = (event: any) => {
    isScroll.value = true
    const scrollTop = event.target.scrollTop
    opacity.value = scrollTop / 100 > 1 ? 1 : scrollTop / 100
    if (scrollTop > 500) {
        upMatchWrap.value?.classList.add('is-show')
    } else {
        upMatchWrap.value?.classList.remove('is-show')
    }
    setTimeout(() => {
        isScroll.value = false
    }, 1000)
}

const changeActiveTab = (index: number) => {
    activeTab.value = index
}

const sayHello = () => {
    showSayHiPopup.value = true
}
onMounted(() => {
    // 组件挂载后的逻辑
    // 模拟骨架屏加载
    setTimeout(() => {
        skeletonLoading.value = false
    }, 2000)
})

onUnmounted(() => {
    // 组件卸载前的清理逻辑
})
</script>
<style lang="scss" scoped>
.app-container {
    position: relative;
    width: 100%;
    height: 100%;
    padding-bottom: 100px;
    box-sizing: border-box;
    -webkit-overflow-scrolling: touch;
    background: #000;
    overflow-y: auto;

    // 骨架屏样式
    &.skeleton-container {
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
        padding: 0;

        .skeleton-header {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            z-index: 10;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        .skeleton-swiper {
            width: 100%;
            height: 530px;
            border-radius: 0 0 20px 20px;
            overflow: hidden;
        }

        .skeleton-anchor-info {
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            margin: 15px;
            border-radius: 20px;

            .skeleton-anchor-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 15px;

                .skeleton-anchor-left {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    gap: 8px;
                }

                .skeleton-anchor-right {
                    flex-shrink: 0;
                }
            }

            .skeleton-anchor-desc {
                margin-bottom: 15px;
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            .skeleton-like-box {
                margin-bottom: 12px;
                border-radius: 15px;
                overflow: hidden;
                background: rgba(255, 255, 255, 0.1);
            }

            .skeleton-tags {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
            }
        }

        .skeleton-tabs {
            background: rgba(255, 255, 255, 0.05);
            margin: 0 15px;
            border-radius: 20px;
            backdrop-filter: blur(10px);

            .skeleton-tab-header {
                padding: 15px;
                display: flex;
                gap: 30px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }

            .skeleton-tab-content {
                padding: 15px;

                .skeleton-moments-item {
                    margin-bottom: 20px;
                    padding: 20px;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 15px;
                    backdrop-filter: blur(5px);

                    .skeleton-moments-header {
                        display: flex;
                        align-items: center;
                        gap: 12px;
                        margin-bottom: 15px;

                        .skeleton-moments-info {
                            display: flex;
                            flex-direction: column;
                            gap: 6px;
                        }
                    }

                    .skeleton-moments-content {
                        display: flex;
                        flex-direction: column;
                        gap: 10px;
                    }
                }
            }
        }

        .skeleton-bottom-operation {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 100px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(20px);
            display: flex;
            align-items: flex-end;
            padding: 0 15px 4px;
            gap: 10px;
            z-index: 1001;
        }
    }

    // 顶部导航栏
    .app-container-header {
        position: fixed;
        width: 100%;
        height: 44px;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 15px;
        box-sizing: border-box;
        z-index: 10;
        transition: background-color 0.3s ease;

        .left-icon,
        .right-icon {
            width: 24px;
            height: 24px;
            cursor: pointer;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
            transition: transform 0.2s ease;

            &:hover {
                transform: scale(1.1);
            }
        }

        .right-icon {
            width: 30px;
            height: 30px;
        }

        .app-container-header-status {
            img {
                height: 26px;
            }
        }
    }

    // 轮播容器
    .anchor-detail {
        width: 100%;
        height: 530px;

        .story-swiper {
            width: 100%;
            height: 100%;

            .story-slide {
                width: 100%;
                height: 100%;

                .image-container {
                    position: relative;
                    width: 100%;
                    height: 100%;

                    .story-image {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        object-position: center;
                        border-radius: 0 0;
                    }
                }
            }

            // 底部导航样式
            .bottom-nav {
                width: 100%;
                position: absolute;
                bottom: 10px;
                right: 15px;
                z-index: 5;
                display: flex;
                align-items: center;
                justify-content: flex-end;
                gap: 10px;


                // 自定义分页器样式
                .swiper-pagination {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    padding: 8px 12px;
                    max-width: 188px;
                    border-radius: 20px;
                    overflow-x: auto;
                    position: static;

                    :deep(.swiper-pagination-bullet) {
                        flex-shrink: 0 !important;
                        width: 40px !important;
                        height: 40px !important;
                        border-radius: 6px !important;
                        overflow: hidden !important;
                        cursor: pointer !important;
                        border: 2px solid transparent !important;
                        transition: all 0.3s ease !important;
                        box-sizing: border-box !important;
                        background: transparent !important;
                        opacity: 1 !important;
                        margin: 0 !important;

                        &.swiper-pagination-bullet-active {
                            border: 1px solid #fff !important;
                        }

                        img {
                            width: 100% !important;
                            height: 100% !important;
                            object-fit: cover !important;
                            display: block !important;
                        }
                    }
                }

                // 图片计数器样式
                .slide-counter {
                    color: #fff;
                    font-size: 14px;
                    font-weight: 500;
                    font-family: var(--font-family-urbanist);
                }
            }
        }
    }

    .anchor-content {
        margin-top: 15px;
        margin-bottom: 30px;

        .content-header {
            padding-inline-start: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            &-left {
                .left-top {
                    display: flex;
                    align-items: center;
                    gap: 4px;

                    .left-top-name {
                        font-size: 20px;
                        font-weight: 700;
                        color: #fff;
                        font-family: var(--font-family-urbanist);
                        line-height: 24px;
                    }

                    .left-top-age {
                        display: flex;
                        align-items: center;
                        gap: 1px;
                        padding: 1px 4px;
                        border-radius: 6px;
                        background-color: $common-color;

                        img {
                            width: 10px;
                            height: 10px;
                        }

                        span {
                            font-size: 10px;
                            font-weight: 800;
                            line-height: 12px;
                            font-family: var(--font-family-urbanist);
                            color: #fff;
                            font-style: italic;
                        }
                    }
                }

                .left-section {
                    margin-top: 8px;
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    font-size: 10px;
                    font-family: var(--font-family-urbanist);
                    font-weight: 900;
                    font-style: italic;
                    color: #fff;

                    .icon-online {
                        padding: 1.5px 6px;
                        background: linear-gradient(90deg, #1DB775 0%, #10B36D 100%);
                        border-radius: 10px;

                    }

                    .icon-hot {
                        padding: 1.5px 4px;
                        background: linear-gradient(90deg, #ff72e7 0%, #ff5280 100%);
                        border-radius: 10px;
                        display: flex;
                        align-items: center;

                        .icon-img {
                            width: 12px;
                            height: 12px;
                        }
                    }

                    .icon-verified {
                        padding: 1.5px 4px;
                        background: linear-gradient(90deg, #7F36FF 0%, #CA38FF 100%);
                        border-radius: 10px;
                        display: flex;
                        align-items: center;

                        .icon-img {
                            width: 14px;
                            height: 14px;
                        }
                    }

                }

                .left-bottom {
                    margin-top: 8px;
                    display: flex;
                    align-items: center;
                    gap: 21px;
                    font-family: var(--font-family-urbanist);
                    font-size: 12px;
                    font-weight: 600;
                    line-height: 14px;
                    color: rgba(255, 255, 255, 0.5);
                }
            }

            &-right {
                display: flex;
                gap: 4px;
                align-items: center;
                padding: 8px 10px 8px;
                background-color: #2B1C18;
                border-radius: 20px 0 0 20px;

                img {
                    width: 20px;
                    height: 20px;
                }

                span {
                    font-size: 15px;
                    font-weight: 700;
                    color: #fff;
                    font-family: var(--font-family-urbanist);
                    line-height: 18px;
                }
            }
        }

        .content-desc {
            padding: 0 15px;
            box-sizing: border-box;
            font-family: var(--font-family-urbanist);
            font-size: 14px;
            font-weight: 500;
            line-height: 17px;
            color: rgba(255, 255, 255, 0.5);
            margin-top: 15px;
        }

        .content-like {
            margin-top: 15px;
            width: 100%;
            padding: 0 15px;
            box-sizing: border-box;

            &-box {
                width: 100%;
                height: 60px;
                display: flex;
                background-color: #1E1D38;
                border-radius: 10px;

                &-left {
                    width: 115px;
                    height: 100%;
                    background-color: #29284E;
                    border-radius: 10px;
                    display: flex;
                    gap: 8px;
                    justify-content: center;
                    align-items: center;

                    &-img {
                        width: 40px;
                        height: 40px;
                    }

                    &-text {
                        display: flex;
                        gap: 3px;
                        flex-direction: column;
                        font-family: var(--font-family-urbanist);

                        &-num {
                            font-size: 20px;
                            font-weight: 700;
                            line-height: 24px;
                            color: #fff;
                        }

                        &-rate {
                            font-size: 12px;
                            font-weight: 600;
                            line-height: 14px;
                            color: rgba(255, 255, 255, 0.5);
                        }
                    }
                }

                &-right {
                    flex: 1;
                    padding: 0 15px;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    gap: 5px;

                    .right-top {
                        width: 100%;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;

                        &-img {
                            width: 18px;
                            height: 18px;
                        }
                    }

                    .right-section {
                        width: 100%;
                        height: 6px;
                        background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                        border-radius: 10px;
                    }

                    .right-bottom {
                        width: 100%;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        font-size: 12px;
                        font-weight: 600;
                        line-height: 14px;
                        font-family: var(--font-family-urbanist);

                        &-like {
                            color: #fff;
                        }

                        &-nolike {
                            color: rgba(255, 255, 255, 0.5);
                        }
                    }
                }
            }
        }

        .content-tag {
            margin-top: 12px;
            padding: 0 15px;
            box-sizing: border-box;

            .content-tag-box {
                width: 100%;
                display: flex;
                flex-wrap: wrap;
                gap: 8px;

                &-item {
                    padding: 5px 8px;
                    background-color: #29284E;
                    border-radius: 20px;
                    font-size: 12px;
                    font-family: var(--font-family-urbanist);
                    font-weight: 600;
                    line-height: 14px;

                    &.item-1 {
                        color: #FC5E85;
                        background: #FC5E8533;
                    }

                    &.item-2 {
                        color: #5293FB;
                        background: #5293FB33;
                    }

                    &.item-3 {
                        color: #28BCB4;
                        background-color: #28BCB433;
                    }

                    &.item-4 {
                        color: #FA944A;
                        background-color: #FA944A33;
                    }

                    &.item-5 {
                        color: #904AFF;
                        background-color: #904AFF33;
                    }

                    &.item-6 {
                        color: #44B02D;
                        background-color: #44B02D33;
                    }
                }

            }
        }
    }

    .tab-wrap {
        width: 100%;

        .tab-wrap-header {
            padding: 5px 15px;
            width: 100%;
            display: flex;
            align-items: center;
            gap: 30px;
            font-size: 18px;
            line-height: 22px;
            font-weight: 700;
            font-family: var(--font-family-urbanist);
            background-color: #000;
            position: sticky;
            top: calc(44px + env(safe-area-inset-top));
            z-index: 1001;

            &-item {
                position: relative;
                color: rgba(255, 255, 255, 0.5);

                &.tab-active {
                    color: #fff;

                    &::after {
                        content: '';
                        position: absolute;
                        bottom: -2px;
                        left: 50%;
                        transform: translateX(-50%);
                        width: 10px;
                        height: 3px;
                        border-radius: 2px;
                        background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                    }
                }

            }
        }

        .tab-content {
            width: 100%;
            overflow-y: auto;
            padding: 10px 15px;
            min-height: calc(100vh - 76px - env(safe-area-inset-top));

            .honor-content {
                width: 100%;
                display: flex;
                flex-direction: column;
                gap: 10px;

                .honor-content-item {
                    width: 100%;
                    height: 44px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    font-family: var(--font-family-urbanist);
                    font-size: 15px;
                    font-weight: 700;

                    &-left {
                        color: #fff;
                    }

                    &-right {
                        display: flex;
                        align-items: center;
                        gap: 2px;
                        color: rgba(255, 255, 255, 0.7);

                        img {
                            width: 20px;
                            height: 20px;
                        }
                    }
                }

                .honor-content-list {
                    display: grid;
                    grid-template-columns: repeat(4, 1fr);
                    gap: 8px;

                    .honor-content-list-item {
                        width: 100%;
                        height: 80px;
                        background-color: rgba($color: #fff, $alpha: .1);
                        border-radius: 6px;
                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                            border-radius: 6px;
                        }
                    }
                }

                .honor-medal-list {
                    display: grid;
                    grid-template-columns: repeat(4, 1fr);
                    gap: 8px;

                    .honor-medal-list-item {
                        width: 100%;
                        height: 56px;
                        background-color: rgba($color: #fff, $alpha: .1);
                        border-radius: 6px;
                        display: flex;
                        align-items: center;
                        img {
                            width: 100%;
                            object-fit: cover;
                        }
                    }
                }
            }
        }
    }

    .up-match-wrap {
        position: fixed;
        width: 68px;
        height: 68px;
        bottom: calc(90px + env(safe-area-inset-bottom));
        right: 20px;
        transform: scale(0);
        transition: all .3s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 1001;

        &.is-show {
            transform: scale(1);
        }

        img {
            width: 100%;
            height: 100%;
        }
    }

    .bottom-operation {
        width: 100%;
        position: fixed;
        bottom: 0;
        height: 100px;
        z-index: 1001;
        display: flex;
        align-items: flex-end;
        padding: 0 15px 4px;
        gap: 10px;
        box-sizing: border-box;

        .follow {

            width: 70px;
            height: 82px;
            padding-bottom: 16px;
            box-sizing: border-box;
            display: flex;
            justify-content: center;
            align-items: flex-end;

            .follow-box {
                width: 100%;
                height: 44px;
                display: flex;
                justify-content: center;
                align-items: center;
                background-color: #1E1D38;
                border-radius: 10px;

                img {
                    width: 24px;
                    height: 24px;
                }
            }

        }

        .btn-call-wrap-vir {
            flex: 1;
            height: 82px;
            position: relative;

            .btn-call-wrap {
                width: 100%;
                height: 100%;
                position: absolute;
                display: flex;
                justify-content: center;
                align-items: flex-end;

                .w {
                    opacity: 0;
                    width: 20px;
                    height: 20px;
                    background: linear-gradient(90deg, #ff8e2d 0%, #ff06ca 100%);
                    position: absolute;
                    border-radius: 16px;
                    animation: opac-animation 3s infinite steps(40);
                }

                .w2 {
                    animation-delay: .3s;
                }

                .btn-call-wrap-button {
                    margin: 16px 0;
                    width: 100%;
                    height: 44px;
                    border-radius: 16px;
                    background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                    animation: call-animation 3s infinite steps(40);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    color: #fff;
                    font-size: 16px;
                    font-weight: 700;
                    font-family: var(--font-family-urbanist);
                    gap: 5px;

                    &-icon-img {
                        width: 30px;
                        height: 30px;
                        animation: call-swing 3s infinite steps(40);
                    }
                }

                .live-call-wrap-button {
                    margin: 16px 0;
                    width: 100%;
                    height: 44px;
                    border-radius: 16px;
                    background: linear-gradient(90deg, #0051E7 0%, #00BEFB 100%);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    color: #fff;
                    font-size: 16px;
                    font-weight: 700;
                    font-family: var(--font-family-urbanist);
                    gap: 4px;

                    &-img {
                        width: 30px;
                        height: 30px;
                    }
                }

                .premium-call-wrap-button {
                    margin: 16px 0;
                    width: 100%;
                    height: 44px;
                    border-radius: 16px;
                    background: linear-gradient(90deg, #FF22F0 0%, #FF246D 100%);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    color: #fff;
                    font-size: 16px;
                    font-weight: 700;
                    font-family: var(--font-family-urbanist);
                    gap: 4px;

                    &-img {
                        width: 30px;
                        height: 30px;
                    }
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .app-container {
        .swiper-pagination {
            .swiper-pagination-bullet {
                width: 28px !important;
                height: 28px !important;
            }
        }

        .action-buttons {
            right: 10px;
            bottom: 80px;

            .action-btn {
                width: 44px;
                height: 44px;
            }
        }
    }
}
</style>