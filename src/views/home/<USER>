<!-- 搜索页面 -->
<template>
    <div class="app-container search-page">
        <div class="search-page-header">
            <div class="search-page-header-left" @click="handleBack">
                <img src="@/assets/common/back.png" alt="">
            </div>
            <div class="search-page-header-center">
                <img src="@/assets/common/search-icon.png" alt="" class="search-icon">
                <input type="text" placeholder="Search">
            </div>
        </div>
        <!---搜索历史--->
        <div class="search-content">
            <div class="search-page-history">
                <div class="search-page-history-title">
                    <span>Popular Tags</span>
                </div>
                <div class="search-page-history-content">
                    <div class="search-page-history-item" v-for="item in 7" :key="item">
                        <span>Good Attitude</span>
                    </div>
                </div>
            </div>
            <!---Recently Viewed-->
            <div class="search-page-recently-viewed">
                <div class="search-page-recently-viewed-title">
                    <span>Recently Viewed</span>
                </div>
                <div class="search-page-recently-viewed-content">
                    <div class="history-item" v-for="i in 3" :key="i">
                        <div class="history-item-bg">
                            <div class="history-item-bg-img">
                                <img src="@/assets/default.png" alt="" class="img-bg">
                            </div>
                            <div class="is-onlie">

                            </div>
                        </div>
                        <span class="history-item-text">SAYRA💎</span>
                    </div>
                </div>
            </div>
            <div class="search-content-more">


                <div class="no-data">
                    <img src="@/assets/common/no-data.svg" alt="">
                    <span>No data ...</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
const router = useRouter()
const handleBack = () => {
    router.go(-1)
}
</script>
<style lang="scss" scoped>
.search-page {
    display: flex;
    flex-direction: column;

    .search-page-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 15px;
        height: 48px;
        gap: 10px;

        .search-page-header-left {
            width: 24px;
            height: 24px;

            img {
                width: 100%;
                height: 100%;
            }
        }

        .search-page-header-center {
            flex: 1;
            height: 36px;
            background: #1E1D38;
            border-radius: 6px;
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 0 8px;

            .search-icon {
                width: 24px;
                height: 24px;
            }

            input {
                flex: 1;
                height: 100%;
                background: transparent;
                border: none;
                outline: none;
                color: #fff;
                font-size: 12px;
            }
        }
    }

    .search-content {
        flex: 1;
        padding: 0 15px;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .search-page-history {
            margin-top: 10px;

            .search-page-history-title {
                font-size: 16px;
                line-height: 19px;
                color: #fff;
            }

            .search-page-history-content {
                margin-top: 10px;
                display: flex;
                flex-wrap: wrap;
                gap: 10px;

                .search-page-history-item {
                    padding: 8px 12px;
                    background: #1E1D38;
                    border-radius: 6px;
                    font-size: 12px;
                    line-height: 14px;

                    color: rgba($color: #fff, $alpha: .7);
                }
            }
        }

        .search-page-recently-viewed {
            margin-top: 30px;

            &-title {
                font-size: 16px;
                line-height: 19px;
                color: #fff;
            }

            &-content {
                margin-top: 10px;
                display: flex;
                gap: 10px;
                overflow-x: auto;

                .history-item {
                    flex-shrink: 0;
                    width: 54px;
                    height: auto;

                    .history-item-bg {
                        width: 100%;
                        height: 54px;
                        border-radius: 50%;
                        background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                        border-radius: 50%;
                        padding: 1.5px;
                        position: relative;
                        display: flex;
                        justify-content: center;
                        align-items: center;

                        .history-item-bg-img {
                            width: 100%;
                            padding: 1.5px;
                            background-color: #1a1a1a;
                            border-radius: 50%;
                            display: flex;
                            justify-content: center;
                            align-items: center;

                            .img-bg {
                                width: 100%;
                                height: 100%;
                                border-radius: 50%;
                            }
                        }

                        .is-onlie {
                            position: absolute;
                            width: 8px;
                            height: 8px;
                            background: #12F38C;
                            border-radius: 50%;
                            right: 5px;
                            bottom: 6px;
                        }
                    }

                    .history-item-text {
                        font-size: 12px;
                        font-weight: 600;
                        color: rgba(255, 255, 255, 0.7);
                        line-height: 14px;
                        text-align: center;
                        margin-top: 7px;
                        white-space: nowrap;
                    }
                }
            }
        }

        .search-content-more {
            flex: 1;
            overflow: hidden;
            .no-data {
                width: 100%;
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;

                img {
                    width: 180px;
                    height: 180px;
                }

                span {
                    margin-top: 15px;
                    font-size: 14px;
                    line-height: 17px;
                    color: rgba($color: #fff, $alpha: .5);
                }
            }
        }
    }

}
</style>