<!-- 匹配 -->
<template>
    <div class="app-container match-page">
        <!-- 关闭按钮 -->
        <div class="close-btn" @click="handleClose">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 6L6 18M6 6L18 18" stroke="white" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round" />
            </svg>
        </div>

        <!-- 轮播图容器 -->
        <div class="swiper-container">
            <swiper :slides-per-view="1.5" :space-between="-200" :centered-slides="true" :loop="true" :autoplay="{
                delay: 3000,
                disableOnInteraction: false,
            }" :grab-cursor="false" :allowTouchMove="false" :modules="modules" :effect="'creative'" :creativeEffect="{
                prev: {
                    shadow: false,
                    translate: ['-88%', '5%', '-2px',],
                    opacity: 1,
                    rotate: [0, 0, -6]
                },
                next: {
                    shadow: false,
                    translate: ['87%', '5%', '-1px'],
                    opacity: 1,
                    rotate: [0, 0, 6]
                },
            }" :watchSlidesProgress="true" class="match-swiper swiper-creative swiper-3d swipe-card">
                <swiper-slide v-for="(user, index) in users" :key="index" class="match-slide">
                    <div class="user-card">
                        <!-- 用户头像 -->
                        <div class="user-avatar">
                            <img :src="user.avatar" :alt="user.name" />
                            <!-- 在线状态指示器 -->
                            <div class="online-indicator" v-if="user.isOnline"></div>
                        </div>
                    </div>
                </swiper-slide>
            </swiper>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Autoplay, Pagination, EffectCreative } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/pagination'
import 'swiper/css/effect-creative'

// Swiper 模块配置
const modules = [Autoplay, Pagination, EffectCreative]

// 模拟用户数据
const users = ref([
    {
        id: 1,
        name: 'Sasha',
        age: 25,
        avatar: 'https://ilivegirl.s3.amazonaws.com/main/52fbe23f97a23c8c4ef7e5587e421f9e20250726055843-medium.jpg',
        question: 'What real connection feels like?',
        isOnline: true
    },
    {
        id: 2,
        name: 'Emma',
        age: 23,
        avatar: 'https://d36zjp6dbikxo9.cloudfront.net/rekognition/27bf22ec6108767e3647be4eb14bbd6020250606092633-medium.jpg',
        question: 'Looking for genuine conversations',
        isOnline: true
    },
    {
        id: 3,
        name: 'Olivia',
        age: 27,
        avatar: 'https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=400&h=600&fit=crop&crop=face',
        question: 'Ready for something real?',
        isOnline: false
    },
    {
        id: 4,
        name: 'Sophie',
        age: 24,
        avatar: 'https://ilivegirl.s3.amazonaws.com/rekognition/bf2443ff579823dcda1ef381fb28641920250725055913-medium.jpg',
        question: 'What makes you smile?',
        isOnline: true
    },
    {
        id: 4,
        name: 'Sophie',
        age: 24,
        avatar: 'https://ilivegirl.s3.amazonaws.com/rekognition/2468383b8ad5fc0581d43227ff75d56120250726074502-medium.jpg',
        question: 'What makes you smile?',
        isOnline: true
    },
    {
        id: 4,
        name: 'Sophie',
        age: 24,
        avatar: 'https://ilivegirl.s3.amazonaws.com/rekognition/333bdd05d279f1c858d84762ecd8b38220250502151730-medium.jpg',
        question: 'What makes you smile?',
        isOnline: true
    },
    {
        id: 4,
        name: 'Sophie',
        age: 24,
        avatar: 'https://d36zjp6dbikxo9.cloudfront.net/rekognition/6af99dbb8662ee884e54ebbc2774370e20250705032908-medium.jpg',
        question: 'What makes you smile?',
        isOnline: true
    },
    {
        id: 4,
        name: 'Sophie',
        age: 24,
        avatar: 'https://ilivegirl.s3.amazonaws.com/main/79ebc489b875357c6ae8dfee20b7836120250727164040-medium.jpg',
        question: 'What makes you smile?',
        isOnline: true
    },
    {
        id: 4,
        name: 'Sophie',
        age: 24,
        avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&h=600&fit=crop&crop=face',
        question: 'What makes you smile?',
        isOnline: true
    },
    {
        id: 4,
        name: 'Sophie',
        age: 24,
        avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&h=600&fit=crop&crop=face',
        question: 'What makes you smile?',
        isOnline: true
    },
    {
        id: 4,
        name: 'Sophie',
        age: 24,
        avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&h=600&fit=crop&crop=face',
        question: 'What makes you smile?',
        isOnline: true
    },
    {
        id: 4,
        name: 'Sophie',
        age: 24,
        avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&h=600&fit=crop&crop=face',
        question: 'What makes you smile?',
        isOnline: true
    }
])

// 处理关闭
const handleClose = () => {
    console.log('Close match page')
    // 这里可以添加路由跳转或关闭逻辑
}

// 处理拒绝
const handleReject = (user: any) => {
    console.log('Reject user:', user.name)
    // 这里可以添加拒绝逻辑
}

// 处理喜欢
const handleLike = (user: any) => {
    console.log('Like user:', user.name)
    // 这里可以添加喜欢逻辑
}
</script>

<style lang="scss" scoped>
.match-page {
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    // 关闭按钮
    .close-btn {
        position: absolute;
        top: 40px;
        right: 20px;
        width: 40px;
        height: 40px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        z-index: 10;

        &:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }
    }

    // 轮播图容器
    .swiper-container {
        width: 100%;
    }

    .match-swiper {
        width: 100%;
        height: 100%;
        overflow: visible;
        perspective: 1200px;

        :deep(.swiper-wrapper) {
            align-items: center;
            transform-style: preserve-3d;
        }

        :deep(.swiper-slide) {
            display: flex;
            align-items: center;
            justify-content: center;
            transform-style: preserve-3d;
            backface-visibility: hidden;
        }

        // 激活状态的卡片
        :deep(.swiper-slide-active) {
            z-index: 3;
        }

        // 前一张和后一张卡片
        :deep(.swiper-slide-prev),
        :deep(.swiper-slide-next) {
            z-index: 2;
        }

        // 其他卡片
        :deep(.swiper-slide:not(.swiper-slide-active):not(.swiper-slide-prev):not(.swiper-slide-next)) {
            z-index: 1;
        }
    }

    // 用户卡片
    .user-card {
        width: 230px;
        height: 357px;
        border-radius: 20px;
        display: flex;
        flex-direction: column;
        align-items: center;
        backdrop-filter: blur(10px);
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
        transform-style: preserve-3d;

        // 用户头像
        .user-avatar {
            width: 230px;
            height: 357px;
            border-radius: 16px;
            overflow: hidden;
            position: relative;
            margin-bottom: 20px;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 16px;
            }
        }
    }
}
</style>