<!-- app-container -->
<template>
    <div class="app-container ranking-page">
        <div class="ranking-page-header">
            <div class="ranking-page-header-left" @click="goBack">
                <img src="@/assets/common/go-back.png" alt="">
            </div>
            <div class="ranking-page-header-right">
                <div class="ranking-page-header-right-tab-item" :class="{ active: activeTab === 0 }"
                    @click="changeTab(0)">
                    Popular
                </div>
                <div class="ranking-page-header-right-tab-item" :class="{ active: activeTab === 1 }"
                    @click="changeTab(1)">
                    Deluxe
                </div>
                <div class="ranking-page-header-right-tab-item" :class="{ active: activeTab === 2 }"
                    @click="changeTab(1)">
                    Intimacy
                </div>
            </div>
        </div>
        <!--整个部分的day 和 weekly切换-->
        <div class="ranking-page-switch">
            <div class="ranking-page-switch-box">
                <div class="ranking-page-switch-box-item" :class="{ active: activeSwitch === 0 }"
                    @click="changeSwitch(0)">
                    Daily
                </div>
                <div class="ranking-page-switch-box-item" :class="{ active: activeSwitch === 1 }"
                    @click="changeSwitch(1)">
                    Weekly
                </div>
                <div class="transition-box" :style="{ transform: `translateX(${activeSwitch * 100}%)` }">

                </div>
            </div>
        </div>
        <!--下面部分-->
        <div class="ranking-page-content">
            <var-swipe ref="rankingSwipe" class="swipe-example" :indicator="false" @change="changeSwipe">
                <var-swipe-item>
                    <PullRefresh v-model="refreshLoading[0]" @refresh="onRefresh(3)" :threshold="60"
                        :success-duration="1500">
                        <div class="popular-content common-content">
                            <div class="top-three">
                                <div class="top-three-box">
                                    <!--第二名-->
                                    <div class="top-three-box-item top-three-box-item-2">
                                        <div class="top-three-box-item-top">
                                            <img src="@/assets/rankings/common-2.png" alt="" class="common-2">
                                            <img src="@/assets/home/<USER>" alt="" class="ranking-avatar">
                                        </div>
                                        <!---是否关注-->
                                        <img src="@/assets/rankings/follow.png" alt="" class="follow-img">
                                        <div class="top-three-box-item-name">
                                            DUNYA🌍
                                        </div>
                                        <!--下面是标签-->
                                        <img src="@/assets/rankings/ranking-tag.png" alt="" class="ranking-tag">
                                    </div>
                                    <!--第一名-->
                                    <div class="top-three-box-item top-three-box-item-1">
                                        <div class="top-three-box-item-top">
                                            <img src="@/assets/rankings/common-1.png" alt="" class="common-2">
                                            <img src="@/assets/home/<USER>" alt="" class="ranking-avatar">
                                        </div>
                                        <!---是否关注-->
                                        <img src="@/assets/rankings/follow.png" alt="" class="follow-img">
                                        <div class="top-three-box-item-name">
                                            DUNYA🌍
                                        </div>
                                        <!--下面是标签-->
                                        <img src="@/assets/rankings/ranking-tag.png" alt="" class="ranking-tag">
                                    </div>
                                    <!--第三名-->
                                    <div class="top-three-box-item top-three-box-item-2">
                                        <div class="top-three-box-item-top">
                                            <img src="@/assets/rankings/common-3.png" alt="" class="common-2">
                                            <img src="@/assets/home/<USER>" alt="" class="ranking-avatar">
                                        </div>
                                        <!---是否关注-->
                                        <img src="@/assets/rankings/follow.png" alt="" class="follow-img">
                                        <div class="top-three-box-item-name">
                                            DUNYA🌍
                                        </div>
                                        <!--下面是标签-->
                                        <img src="@/assets/rankings/ranking-tag.png" alt="" class="ranking-tag">
                                    </div>
                                </div>
                            </div>
                            <div class="popular-content-list">
                                <div class="popular-content-list-item" v-for="item in 100" :key="item">
                                    <div class="popular-content-list-item-num">
                                        {{ item + 3 > 10 ? item + 3 : '0' + (item + 3) }}
                                    </div>
                                    <div class="popular-content-list-item-center">
                                        <img src="@/assets/home/<USER>" alt=""
                                            class="popular-content-list-item-avatar" />
                                        <div class="center-right">
                                            <div class="center-right-name">
                                                Visitor-323232
                                            </div>
                                            <img src="@/assets/rankings/ranking-tag.png" alt="" class="ranking-tag">
                                            <div class="center-right-about">
                                                IM NEW HERE ❤️ I CAN SURPRISIM NEW HERE ❤️ I CAN SURPRISIM NEW
                                                HERE ❤️ I CAN SURPRIS
                                            </div>
                                        </div>
                                    </div>
                                    <div class="popular-content-list-item-right">
                                        <img src="@/assets/rankings/follow.png" alt="" class="follow-img" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </PullRefresh>
                </var-swipe-item>
                <var-swipe-item>

                </var-swipe-item>
                <var-swipe-item>

                </var-swipe-item>
            </var-swipe>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import PullRefresh from '@/components/PullRefresh.vue'
const router = useRouter()

const refreshLoading = ref([false, false, false])
const goBack = () => {
    router.back()
}
const rankingSwipe = ref<any>(null)
const activeTab = ref(0)
const activeSwitch = ref(0)
const changeTab = (index: number) => {
    activeTab.value = index
    rankingSwipe.value.to(index)
}
const changeSwitch = (index: number) => {
    activeSwitch.value = index
}
const changeSwipe = (index: number) => {
    console.log(index)
    activeTab.value = index
}
const onRefresh = (index: number) => {
    refreshLoading.value[index] = true
    setTimeout(() => {
        refreshLoading.value[index] = false
    }, 1500)
}
const scrollEvent = () => {
    console.log('scroll event triggered')
    const scrollDom = document.querySelector('.common-content') as HTMLElement
    if (scrollDom) {
        console.log('Scroll distance:', scrollDom.scrollTop)
        if (scrollDom.scrollTop > 100) {
            console.log('Scroll distance > 100:', scrollDom.scrollTop)
        }
    }
}

// 添加window滚动事件监听
const windowScrollEvent = () => {
    console.log('window scroll:', window.scrollY)
}

onMounted(() => {
    window.addEventListener('scroll', scrollEvent)
})
onUnmounted(() => {
    // 清理所有可能的滚动事件监听器
    window.removeEventListener('scroll', scrollEvent)

})
</script>
<style lang="scss" scoped>
.ranking-page {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    // background: url('@/assets/rankings/bg.png') no-repeat;
    // background-size: 100% auto;

    .ranking-page-header {
        height: 44px;
        padding: 0 15px;
        box-sizing: border-box;
        display: flex;
        flex-direction: row;
        align-items: center;
        z-index: 10;
        position: fixed;
        top: env(safe-area-inset-top);
        left: 0;

        &-left {
            img {
                width: 24px;
                height: 24px;
            }
        }

        &-right {
            margin-left: 25px;
            flex: 1;
            display: flex;
            align-items: center;
            gap: 20px;
            font-size: 18px;
            font-weight: 700;
            line-height: 22px;

            &-tab-item {
                color: rgba($color: #fff, $alpha: .5);
                position: relative;
                cursor: pointer;
                transition: color 0.3s ease;

                &.active {
                    color: #fff;

                    &::before {
                        content: '';
                        position: absolute;
                        left: 0;
                        right: 0;
                        margin: 0 auto;
                        bottom: -4px;
                        width: 10px;
                        height: 3px;
                        border-radius: 2px;
                        background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                    }
                }
            }
        }
    }

    .ranking-page-switch {
        position: fixed;
        top: calc(env(safe-area-inset-top) + 56px);
        width: 100%;
        display: flex;
        justify-content: center;
        z-index: 10;

        .ranking-page-switch-box {
            width: 150px;
            height: 30px;
            background-color: rgba(30, 29, 56, .7);
            border-radius: 30px;
            display: flex;
            align-items: center;
            position: relative;

            &-item {
                flex: 1;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                font-weight: 700;
                color: rgba(255, 255, 255, 0.5);
                position: relative;
                z-index: 2;

                &.active {
                    color: #fff;
                }
            }

            .transition-box {
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                width: 50%;
                height: 100%;
                background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                border-radius: 30px;
                transition: transform 0.3s ease;
                z-index: 1;
            }
        }
    }

    .ranking-page-content {
        flex: 1;
        background-color: #0f0f11;
        overflow: auto;

        .var-swipe {
            width: 100%;
            height: 100%;

            .common-content {
                width: 100%;
                height: 100%;
                padding-top: calc(env(safe-area-inset-top) + 110px);
                box-sizing: border-box;
                background: url('@/assets/rankings/bg.png') no-repeat;
                background-size: 100% auto;
                display: flex;
                flex-direction: column;

                .top-three {
                    padding: 0 17px;
                    box-sizing: border-box;

                    &-box {
                        width: 100%;
                        height: 200px;
                        background: url('@/assets/rankings/top-three-bg.png') no-repeat;
                        background-size: 100% auto;
                        background-position: bottom;
                        display: flex;
                        justify-content: space-between;
                        gap: 18px;
                        padding: 0 13px;
                        box-sizing: border-box;

                        &-item {
                            display: flex;
                            flex-direction: column;
                            align-items: center;

                            .top-three-box-item-top {
                                position: relative;
                                display: flex;
                                justify-content: center;
                                align-items: center;

                                .common-2 {
                                    position: absolute;
                                    left: 0;
                                    top: 0;
                                    width: 100%;
                                    height: 100%;
                                }

                                .ranking-avatar {
                                    object-fit: cover;
                                    border-radius: 50%;
                                }


                            }

                            &.top-three-box-item-2 {
                                margin-top: 31px;

                                .top-three-box-item-top {
                                    width: 90px;
                                    height: 90px;

                                    .common-2 {
                                        position: absolute;
                                        left: 0;
                                        top: 0;
                                        width: 100%;
                                        height: 100%;
                                    }

                                    .ranking-avatar {
                                        width: 70px;
                                        height: 70px;
                                    }
                                }

                            }

                            &.top-three-box-item-1 {

                                .top-three-box-item-top {
                                    width: 100px;
                                    height: 100px;

                                    .ranking-avatar {
                                        width: 80px;
                                        height: 80px;
                                    }
                                }
                            }



                            .follow-img {
                                margin-top: 2px;
                                width: 24px;
                                height: 20px;
                            }

                            .top-three-box-item-name {
                                margin-top: 4px;
                                font-size: 15px;
                                font-weight: 700;
                                line-height: 18px;
                                color: #fff;
                            }

                            .ranking-tag {
                                margin-top: 4px;
                                width: 49px;
                                height: 14px;
                            }
                        }
                    }

                }

                .popular-content-list {
                    margin-top: -5px;
                    padding: 20px 15px;
                    box-sizing: border-box;
                    flex: 1;
                    background: url('@/assets/rankings/commmon-other-ranking.png') no-repeat;
                    background-size: 100% auto;
                    background-position: top;

                    .popular-content-list-item {
                        margin-top: 20px;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;

                        .popular-content-list-item-num {
                            font-size: 15px;
                            font-weight: 700;
                            line-height: 18px;
                            color: #FDF6AE;
                            margin-right: 16px;
                        }

                        .popular-content-list-item-center {
                            flex: 1;
                            display: flex;
                            align-items: center;
                            gap: 10px;
                            overflow: hidden;

                            .popular-content-list-item-avatar {
                                flex-shrink: 0;
                                width: 44px;
                                height: 44px;
                                border-radius: 50%;
                                object-fit: cover;
                            }

                            .center-right {
                                flex: 1;
                                display: flex;
                                flex-direction: column;
                                gap: 2px;
                                overflow: hidden;

                                .center-right-name {
                                    font-size: 15px;
                                    font-weight: 700;
                                    line-height: 18px;
                                    color: #fff;
                                }

                                .ranking-tag {
                                    height: 14px;
                                    object-fit: contain;
                                    object-position: left;
                                }

                                .center-right-about {
                                    font-size: 12px;
                                    font-weight: 600;
                                    line-height: 14px;
                                    color: rgba($color: #fff, $alpha: .7);
                                    overflow: hidden;
                                    text-overflow: ellipsis;
                                    white-space: nowrap;
                                    width: 100%;
                                }
                            }
                        }

                        .popular-content-list-item-right {
                            margin-left: 40px;

                            .follow-img {
                                width: 24px;
                                height: 20px;
                            }
                        }
                    }
                }

            }
        }
    }

}
</style>