<!--  -->
<template>
    <div class="app-container">
        <div class="common-nav">
            <div class="common-nav-left" @click="goBack">
                <img src="@/assets/common/go-back.png" alt="">
            </div>
            <div class="common-nav-title">
                Edit Profile
            </div>
            <div class="common-nav-right" @click="saveProfile">
                <span>Save</span>
            </div>
        </div>
        <!--edit-profile-content-->
        <div class="edit-profile-content">
            <form>
                <div class="form-item">
                    <label for="">Profile photos (maximum 7 pics)</label>
                    <div class="form-item-content">
                        <div class="form-item-content-item" v-if="hasCoverImg">
                            <img src="@/assets/common/avatar-default.png" alt="" class="avatar-img">
                            <div class="cover-img-desc">
                                Cover
                            </div>
                        </div>
                        <div class="form-item-content-item">
                            <img src="@/assets/common/avatar-default.png" alt="" class="avatar-img">
                            <div class="cover-img-desc">
                                Avatar
                            </div>
                        </div>
                        <div class="form-item-content-item" v-for="item in 2" :key="item">
                            <img src="@/assets/home/<USER>" alt="" class="avatar-img">
                            <img src="@/assets/common/delete-img.png" alt="" class="delete-img">
                        </div>
                        <div class="add-photo">
                            <img src="@/assets/common/upload-img.png" alt="">
                        </div>
                    </div>
                </div>
                <div class="form-item">
                    <label for="">Basic Information</label>
                </div>
                <div class="form-item">
                    <label for="" class="form-item-label-white">Nickname</label>
                    <div class="form-item-input">
                        <div class="form-item-content-input-box">
                            <input v-model="profileInfo.nickname" type="text" class="form-item-content-input">
                        </div>
                        <div class="form-item-content-operation">
                            <img src="@/assets/common/clear-input.png" alt="" v-if="profileInfo.nickname"
                                @click="profileInfo.nickname = ''">
                        </div>
                    </div>
                </div>
                <div class="form-item">
                    <label for="" class="form-item-label-white">Basic Information</label>
                    <div class="form-item-input">
                        <div class="form-item-content-input-box">
                            <input v-model="profileInfo.gender" readonly type="text" class="form-item-content-input"
                                @click="changeSelectGender">
                        </div>
                        <div class="form-item-content-operation">
                            <img src="@/assets/profile/profile-arrow.png" class="profile-arrow">
                        </div>
                    </div>
                </div>
                <div class="form-item">
                    <label for="" class="form-item-label-white">Age</label>
                    <div class="form-item-input" @click="changeSelectAge">
                        <div class="form-item-content-input-box">
                            <input v-model="profileInfo.age" readonly type="text" class="form-item-content-input">
                        </div>
                        <div class="form-item-content-operation">
                            <img src="@/assets/profile/profile-arrow.png" class="profile-arrow">
                        </div>
                    </div>
                </div>
                <div class="form-item">
                    <label for="" class="form-item-label-white">About me (within 120 characters)</label>
                    <div class="form-item-input form-item-input-textarea">
                        <div class="form-item-content-input-box">
                            <textarea v-model="profileInfo.aboutMe" placeholder="Hope I can brighten up your day."
                                :maxlength="120" class="form-item-content-input"></textarea>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <select-gender-dialog ref="selectGenderDialog" :show="selectGenderDialogShow"
            @close="selectGenderDialogShow = false" @confirm="selectGenderFunc"></select-gender-dialog>
        <select-age-dialog ref="selectAgeDialog" :show="selectAgeDialogShow" @close="selectAgeDialogShow = false"
            @confirm="selectAgeFunc"></select-age-dialog>

        <!---不是VIP 提示-->
        <div class="vip-tip">
            <div class="vip-tip-left">
                Wanna stand out among others?
            </div>
            <div class="vip-tip-right">
                <img src="@/assets/profile/svip.png" alt="" class="svip-icon">
                <span>Get SVIP</span>
                <img src="@/assets/profile/get-svip-arrow.png" alt="" class="get-svip-arrow">
            </div>
        </div>
        <Snackbar :show="showMessage" @close="showMessage = false">
            <template #title>
                Success
            </template>
            <template #desc>
                Email Binding Successful!
            </template>
        </Snackbar>
    </div>
</template>

<script lang="ts">
import selectGenderDialog from './components/selectGenderDialog.vue'
import selectAgeDialog from './components/selectAgeDialog.vue'
import Snackbar from '@/components/snackbar/snackbar.vue'
export default {

    data() {
        return {
            showMessage: false,
            hasCoverImg: true,
            profileInfo: {
                nickname: '',
                gender: 'male',
                age: '' as string | number,
                aboutMe: ''
            },
            selectGenderDialogShow: false,
            selectAgeDialogShow: false
        };
    },

    components: {
        selectGenderDialog,
        selectAgeDialog,
        Snackbar
    },

    computed: {},

    mounted() { },

    methods: {
        /**
         * 选择性别确认
         */
        selectGenderFunc(gender: string) {
            this.profileInfo.gender = gender
            this.selectGenderDialogShow = false
        },
        /**
         * 选择性别
         */
        changeSelectGender() {

            (this.$refs.selectGenderDialog as any).selectGender = this.profileInfo.gender
            this.selectGenderDialogShow = true
        },
        changeSelectAge() {
            this.selectAgeDialogShow = true
        },
        selectAgeFunc(age: number) {
            this.profileInfo.age = age
            this.selectAgeDialogShow = false
        },
        saveProfile() {
            this.showMessage = true
        },
        goBack() {
            this.$router.back()
        }
    }
}

</script>
<style lang='scss' scoped>
.app-container {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    font-family: var(--font-family-urbanist);

    .common-nav {
        width: 100%;
        height: 44px;
        padding: 0 15px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;

        &-left {
            img {
                width: 24px;
                height: 24px;
            }
        }

        &-title {
            font-size: 18px;
            font-weight: 700;
            line-height: 22px;
            color: #fff;
        }

        &-right {
            font-size: 16px;
            font-weight: 700;
            color: $common-color;
        }
    }

    .edit-profile-content {
        margin-top: 10px;
        flex: 1;
        overflow: auto;
        padding: 0 15px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;

        form {
            .form-item {
                margin-bottom: 20px;

                label {
                    display: block;
                    font-size: 12px;
                    font-weight: 600;
                    line-height: 14px;
                    color: rgba($color: #fff, $alpha: .5);
                    margin-bottom: 10px;

                    &.form-item-label-white {
                        color: #fff;
                    }
                }

                .form-item-content {
                    display: flex;
                    gap: 8px;
                    align-items: center;
                    overflow-x: auto;

                    .form-item-content-item {
                        flex-shrink: 0;
                        width: 70px;
                        height: 70px;
                        border-radius: 10px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        background-color: #1E1D38;
                        position: relative;

                        .avatar-img {
                            width: 100%;
                            height: 100%;
                            border-radius: 10px;
                            object-fit: cover;
                        }

                        .delete-img {
                            position: absolute;
                            top: 4px;
                            right: 4px;
                            width: 12px;
                            height: 12px;
                        }

                        .cover-img-desc {
                            position: absolute;
                            bottom: 7px;
                            height: 14px;
                            background-color: #131323;
                            border-radius: 10px;
                            font-size: 12px;
                            font-weight: 600;
                            line-height: 14px;
                            color: #fff;
                            padding: 0 6px;
                        }
                    }

                    .add-photo {
                        flex-shrink: 0;
                        width: 70px;
                        height: 70px;
                        border-radius: 10px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        background-color: #1E1D38;
                        position: relative;

                        img {
                            width: 12px;
                        }
                    }
                }

                .form-item-input {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    height: 50px;
                    padding: 0 15px;
                    background-color: #1E1D38;
                    border-radius: 10px;

                    &.form-item-input-textarea {
                        height: 130px;
                    }

                    .form-item-content-input-box {
                        flex: 1;
                        height: 100%;

                        input,
                        textarea {
                            font-size: 15px;
                            font-weight: 700;
                            width: 100%;
                            height: 100%;
                            background-color: transparent;
                            border: none;
                            outline: none;
                            color: #fff;
                        }

                        textarea {
                            font-size: 14px;
                            font-weight: 500;
                            padding: 10px;
                        }
                    }



                    .form-item-content-operation {
                        img {
                            width: 16px;
                            height: 16px;
                        }

                        .profile-arrow {
                            width: 24px;
                            height: 24px;
                        }
                    }
                }
            }
        }
    }

    .vip-tip {
        width: 100%;
        padding: 6px 15px;
        box-sizing: border-box;
        background: linear-gradient(90deg, #F6C85B 0%, #FDE9A7 100%);
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: fixed;
        bottom: env(safe-area-inset-bottom);

        &-left {
            font-size: 13px;
            font-weight: 800;
            line-height: 16px;
            color: #481F00;
        }

        &-right {
            display: flex;
            align-items: center;
            padding: 2px 6px;
            background-color: #3F3429;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            line-height: 14px;
            color: #FDECB7;
            gap: 1px;

            .svip-icon {
                width: 20px;
                height: 20px;
            }

            .get-svip-arrow {
                width: 12px;
                height: 12px;
            }
        }
    }
}
</style>