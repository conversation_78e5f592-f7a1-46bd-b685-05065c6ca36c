<!-- 个人中心 -->
<template>
    <div class="app-container profile-container">
        <!--头部的设置和编辑-->
        <div class="profile-header">
            <div class="profile-header-setting">
                <img src="@/assets/profile/setting.png" alt="" class="profile-header-setting-img"
                    @click="goRouterName('Setting')">
            </div>
            <div class="profile-header-edit" @click="goRouterName('EditProfile')">
                <img src="@/assets/profile/edit.png" alt="" class="profile-header-edit-img">
            </div>

        </div>
        <!--头像和昵称-->
        <div class="profile-avatar">
            <div class="profile-avatar-img">
                <img src="@/assets/common/avatar-default.png" alt="" class="profile-avatar-img-img">
            </div>
            <div class="profile-avatar-info">
                <div class="profile-avatar-info-name">
                    <span>Visitor-22323</span>
                    <img src="@/assets/profile/no-svip.png" alt="" class="profile-avatar-info-name-img">
                </div>
                <div class="profile-avatar-info-more">
                    <div class="profile-avatar-info-more-coin">
                        <span>100</span>
                    </div>
                    <div class="profile-avatar-info-more-age">
                        <img src="@/assets/home/<USER>" alt="" class="profile-avatar-info-more-age-img">
                        <span>18</span>
                    </div>
                    <div class="profile-avatar-info-more-id">
                        <span>ID:23345564545</span>
                    </div>
                </div>
            </div>
        </div>
        <!--关注和粉丝-->
        <div class="profile-follow">
            <div class="profile-follow-item">
                <span class="profile-follow-item-num">100</span>
                <span class="profile-follow-item-text">Following</span>

            </div>
            <div class="profile-follow-line">

            </div>
            <div class="profile-follow-item">
                <span class="profile-follow-item-num">100</span>
                <span class="profile-follow-item-text">Followers</span>

            </div>
        </div>
        <!--vip和recharge-->
        <div class="profile-vip-recharge">
            <div class="profile-vip-recharge-item svip">
                <div class="svip-title">
                    <img src="@/assets/profile/svip-title.png" alt="">
                </div>
                <div class="svip-content">
                    <span>Free coins & messages</span>
                    <img src="@/assets/profile/svip-arrow.png" alt="">
                </div>
            </div>
            <div class="profile-vip-recharge-item recharge" @click="goRouterName('Wallet')">
                <div class="recharge-title">
                    <span>Recharge</span>
                </div>
                <div class="recharge-content">
                    <span class="recharge-content-text">100</span>
                    <img src="@/assets/profile/svip-arrow.png" alt="">
                </div>
                <img src="@/assets/profile/recharge-bag.png" alt="" class="recharge-bag">
            </div>
        </div>
        <!---下面的选项-->
        <div class="profile-menu">
            <!--store-->
            <div class="profile-menu-item" @click="goStore('store')">
                <div class="profile-menu-item-left">
                    <img src="@/assets/profile/store.png" alt="" class="profile-menu-item-left-icon">
                    <span class="profile-menu-item-left-text">Store</span>
                </div>
                <div class="profile-menu-item-right">
                    <img src="@/assets/profile/enter-effect.png" alt=""
                        class="profile-menu-item-right-icon enter-effect">
                </div>
            </div>
            <!--my-bag-->
            <div class="profile-menu-item" @click="goStore('myBag')">
                <div class="profile-menu-item-left">
                    <img src="@/assets/profile/my-bag.png" alt="" class="profile-menu-item-left-icon">
                    <span class="profile-menu-item-left-text">My bag</span>
                </div>
                <div class="profile-menu-item-right">
                    <img src="@/assets/profile/profile-arrow.png" alt="" class="profile-menu-item-right-icon">
                </div>
            </div>
            <!--my-level-->
            <div class="profile-menu-item" @click="goRouterName('MyLevel')">
                <div class="profile-menu-item-left">
                    <img src="@/assets/profile/my-level.png" alt="" class="profile-menu-item-left-icon">
                    <span class="profile-menu-item-left-text">My level</span>
                </div>
                <div class="profile-menu-item-right">
                    <img src="@/assets/profile/profile-arrow.png" alt="" class="profile-menu-item-right-icon">
                </div>
            </div>
            <!--Consumption Record-->
            <div class="profile-menu-item" @click="goRouterName('ConsumptionRecord')">
                <div class="profile-menu-item-left">
                    <img src="@/assets/profile/consumption-record.png" alt="" class="profile-menu-item-left-icon">
                    <span class="profile-menu-item-left-text">Consumption Record</span>
                </div>
                <div class="profile-menu-item-right">
                    <img src="@/assets/profile/profile-arrow.png" alt="" class="profile-menu-item-right-icon">
                </div>
            </div>
            <!--Blacklist-->
            <div class="profile-menu-item" @click="goRouterName('BlackList')">
                <div class="profile-menu-item-left">
                    <img src="@/assets/profile/blacklist.png" alt="" class="profile-menu-item-left-icon">
                    <span class="profile-menu-item-left-text">Blacklist</span>
                </div>
                <div class="profile-menu-item-right">
                    <img src="@/assets/profile/profile-arrow.png" alt="" class="profile-menu-item-right-icon">
                </div>
            </div>
            <!--My Account-->
            <div class="profile-menu-item" @click="goRouterName('Account')">
                <div class="profile-menu-item-left">
                    <img src="@/assets/profile/my-account.png" alt="" class="profile-menu-item-left-icon">
                    <span class="profile-menu-item-left-text">My Account</span>
                </div>
                <div class="profile-menu-item-right">
                    <div class="profile-menu-item-right-desc">
                        <img src="@/assets/profile/account-unprotected.png" alt="">
                        <span>Unprotected</span>
                    </div>
                    <img src="@/assets/profile/profile-arrow.png" alt="" class="profile-menu-item-right-icon">
                </div>
            </div>
            <!--connect-us-->
            <div class="profile-menu-item">
                <div class="profile-menu-item-left">
                    <img src="@/assets/profile/contact.png" alt="" class="profile-menu-item-left-icon">
                    <span class="profile-menu-item-left-text">Contact us</span>
                </div>
                <div class="profile-menu-item-right">
                    <img src="@/assets/profile/profile-arrow.png" alt="" class="profile-menu-item-right-icon">
                </div>
            </div>
        </div>
        <BindEmailTip :show="showBindEmailTip" />
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import BindEmailTip from './components/bindEmailTips.vue'
const router = useRouter()

const showBindEmailTip = ref(false)
const goRouterName = (routerName: string) => {
    router.push({
        name: routerName
    })
}
const goStore = (type: string) => {
    router.push({
        path: '/memberShip/store',
        query: {
            nav: type
        }
    })
}
</script>
<style lang="scss" scoped>
.profile-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: 0px 15px calc(48px + env(safe-area-inset-bottom));
    box-sizing: border-box;
    font-family: var(--font-family-urbanist);

    .profile-header {
        padding-top: 11px;
        width: 100%;
        display: flex;
        flex-direction: row-reverse;

        gap: 20px;

        &-edit {
            width: 24px;
            height: 24px;

            &-img {
                width: 100%;
                height: 100%;
            }
        }

        &-setting {
            width: 24px;
            height: 24px;
            position: relative;

            &-img {
                width: 100%;
                height: 100%;
            }
        }
    }

    .profile-avatar {
        width: 100%;
        height: 100px;
        display: flex;
        align-items: center;
        gap: 15px;

        &-img {
            width: 70px;
            height: 70px;

            &-img {
                width: 100%;
                height: 100%;
                border-radius: 50%;
            }
        }

        &-info {
            &-name {
                display: flex;
                align-items: center;

                span {
                    font-size: 18px;
                    font-weight: 700;
                    line-height: 22px;
                    color: #fff;
                }

                &-img {
                    width: auto;
                    height: 16px;
                    margin-left: 5px;
                }
            }

            &-more {
                margin-top: 10px;
                display: flex;
                align-items: center;
                gap: 5px;

                &-coin {
                    width: 38px;
                    height: 12px;
                    padding-inline-end: 4px;
                    box-sizing: border-box;
                    background: url('@/assets/profile/coin-bg.webp') no-repeat;
                    background-size: 100% 100%;
                    font-size: 10px;
                    line-height: 12px;
                    font-weight: 700;
                    text-align: end;
                    color: #fff;
                }

                &-age {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 1px;
                    height: 12px;
                    padding: 0 4px;
                    background-color: #448FFC;
                    border-radius: 6px;

                    &-img {
                        width: 12px;
                        height: 12px;
                    }

                    span {
                        font-size: 10px;
                        font-weight: 800;
                        font-style: italic;
                        color: #fff;
                    }
                }

                &-id {
                    height: 20px;
                    padding: 0 6px;
                    background-color: #1E1D38;
                    border-radius: 13px;

                    span {
                        font-size: 10px;
                        font-weight: 700;
                        line-height: 20px;
                        color: #fff;
                    }
                }
            }
        }
    }

    .profile-follow {
        width: 100%;
        margin-top: 30px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-family: var(--font-family-urbanist);

        &-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            &-num {
                font-size: 18px;
                font-weight: 700;
                color: #fff;
            }

            &-text {
                margin-top: 6px;
                font-size: 12px;
                font-weight: 600;
                line-height: 14px;
                color: rgba($color: #fff, $alpha: .5);
            }

        }

        &-line {
            width: 1px;
            height: 44px;
            background-color: rgba($color: #fff, $alpha: .15);
        }
    }

    .profile-vip-recharge {
        width: 100%;
        margin-top: 30px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 9px;


        &-item {
            flex: 1;
            flex-shrink: 0;
            height: 80px;
            background-color: #1E1D38;
            border-radius: 13px;

            &.svip {
                background: url('@/assets/profile/svip-bg.png') no-repeat;
                background-size: 100% 100%;
                padding: 16px 10px 0 10px;
                box-sizing: border-box;

                .svip-title {
                    width: 56px;
                    height: 16px;

                    img {
                        width: 100%;
                        height: 100%;
                    }
                }

                .svip-content {
                    margin-top: 15.25px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    span {
                        font-size: 12px;
                        font-weight: 600;
                        line-height: 14px;
                        color: #fff;
                        white-space: nowrap;
                    }

                    img {
                        width: 16px;
                        height: 16px;
                    }
                }

            }

            &.recharge {
                background: url('@/assets/profile/recharge-bg.png') no-repeat;
                background-size: 100% 100%;
                padding: 16px 10px 0 10px;
                box-sizing: border-box;
                position: relative;

                .recharge-title {
                    display: flex;
                    // align-items: center;
                    justify-content: space-between;

                    span {
                        font-size: 18px;
                        font-weight: 700;
                        line-height: 22px;
                        color: #fff;
                    }

                }

                .recharge-content {
                    margin-top: 5px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    &-text {
                        font-size: 18px;
                        font-weight: 700;
                        line-height: 22px;
                        color: #fff;
                    }

                    img {
                        width: 16px;
                        height: 16px;
                    }
                }

                .recharge-bag {
                    position: absolute;
                    top: 5px;
                    right: 6px;
                    width: 56px;
                    height: 50px;
                }
            }
        }
    }

    .profile-menu {
        margin-top: 24px;

        &-item {
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;

            &-left {
                display: flex;
                align-items: center;
                gap: 8px;

                &-icon {
                    width: 30px;
                    height: 30px;
                }

                &-text {
                    color: #fff;
                    font-size: 15px;
                    font-weight: 700;
                    line-height: 18px;
                }
            }

            &-right {
                display: flex;
                align-items: center;
                gap: 4px;

                &-icon {
                    width: 24px;
                    height: 24px;
                }

                .enter-effect {
                    width: 44px;
                    height: 16px;
                }

                &-desc {
                    display: flex;
                    align-items: center;
                    font-size: 15px;
                    color: #FFD016;
                    font-weight: 700;

                    img {
                        width: 20px;
                        height: 20px;
                    }
                }
            }
        }
    }
}
</style>