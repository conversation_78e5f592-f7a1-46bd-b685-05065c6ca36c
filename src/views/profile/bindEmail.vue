<!-- 设置页面 -->
<template>
    <div class="app-container bind-email">
        <headerNav title=""></headerNav>
        <div class="bind-email-content">
            <div class="bind-email-content-header">
                <div class="bind-email-content-header-item"
                    :class="{ 'bind-email-content-header-item-active': bindStep >= 1 }">

                </div>
                <div class="bind-email-content-header-item"
                    :class="{ 'bind-email-content-header-item-active': bindStep >= 2 }">

                </div>
            </div>
            <!---第一个步骤-->
            <div v-if="bindStep === 1">
                <div class="bind-email-content-title">
                    <span>Bind your email</span>
                </div>
                <form>
                    <div class="form-item">
                        <label for="" class="form-item-label-white">Email</label>
                        <div class="form-item-input">
                            <div class="form-item-content-input-box">
                                <input v-model="bindEmailInfo.email" type="text" class="form-item-content-input">
                            </div>
                            <div class="form-item-content-operation">
                                <img src="@/assets/common/clear-input.png" alt="" v-if="bindEmailInfo.email"
                                    @click="bindEmailInfo.email = ''">
                            </div>
                        </div>
                    </div>
                    <div class="form-item">
                        <label for="" class="form-item-label-white">Password</label>
                        <div class="form-item-input">
                            <div class="form-item-content-input-box">
                                <input v-model="bindEmailInfo.password" type="password" class="form-item-content-input">
                            </div>
                            <div class="form-item-content-operation">
                                <img src="@/assets/common/clear-input.png" alt="" v-if="bindEmailInfo.password"
                                    @click="bindEmailInfo.password = ''">
                            </div>
                        </div>
                    </div>
                    <div class="bind-email-content-button">
                        <button class="bind-email-content-button-item" type="button" @click="changeStep"
                            :disabled="!bindEmailInfo.email || !bindEmailInfo.password"
                            :class="{ 'bind-button-active': bindEmailInfo.email && bindEmailInfo.password }">
                            <span>Next</span>
                        </button>
                    </div>
                </form>
            </div>
            <!---第二个步骤-->
            <div v-if="bindStep === 2">
                <div class="bind-email-content-title">
                    <span>We sent you a verification code</span>
                </div>
                <div class="bind-email-content-code">
                    <div class="bind-email-content-code-label">
                        Enter it below to verify your eamil
                    </div>
                    <VerificationCodeInput v-model="bindEmailInfo.code" :length="6" />
                    <div class="bind-email-content-code-desc">
                        Didn't receive the email? <span>{{ sendText }}</span>
                    </div>
                    <div class="bind-email-content-button">
                        <button class="bind-email-content-button-item" type="button"
                            :disabled="bindEmailInfo.code.length !== 6"
                            :class="{ 'bind-button-active': bindEmailInfo.code.length === 6 }">
                            <span>Next</span>
                        </button>
                    </div>
                </div>
                <div class="bind-email-content-desc">
                    <img src="@/assets/common/tip.png" alt="">
                    <span>If you haven't received the verification code. please check your spam folder.</span>
                </div>
            </div>
        </div>
        <bindEmailConfirm :show="bindEmailConfirmShow" @confirm="confirm" @update:show="bindEmailConfirmShow = false" />
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import headerNav from '@/components/headerNav/headerNav.vue'
import bindEmailConfirm from './components/bindEmailConfirm.vue'
import VerificationCodeInput from '@/components/VerificationCodeInput.vue'
const bindStep = ref(1)
const bindEmailConfirmShow = ref(false)
const sendText = ref('Resend')
const bindEmailInfo = ref({
    email: '',
    password: '',
    code: ''
})

const changeStep = () => {
    bindEmailConfirmShow.value = true
}
const confirm = () => {
    bindEmailConfirmShow.value = false
    bindStep.value++
}
</script>
<style lang="scss" scoped>
.app-container {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    font-family: var(--font-family-urbanist);

    .bind-email-content {
        margin-top: 3px;
        flex: 1;
        padding: 0 15px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;

        &-header {
            display: flex;
            align-items: center;
            gap: 9px;

            &-item {
                flex: 1;
                height: 2px;
                background: #FFFFFF66;
                transition: background 0.3s ease;

                &.bind-email-content-header-item-active {
                    background: $common-color;
                }
            }
        }

        .bind-email-content-title {
            margin-top: 40px;
            font-size: 22px;
            font-weight: 600;
            line-height: 26px;
            color: #fff;

        }

        form {
            margin-top: 60px;

            .form-item {
                margin-bottom: 20px;

                label {
                    display: block;
                    font-size: 12px;
                    font-weight: 600;
                    line-height: 14px;
                    color: #fff;
                    margin-bottom: 10px;
                }

                .form-item-content {
                    display: flex;
                    gap: 8px;
                    align-items: center;
                }

                .form-item-input {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    height: 50px;
                    padding: 0 15px;
                    background-color: #1E1D38;
                    border-radius: 10px;
                    box-sizing: border-box;

                    .form-item-content-input-box {
                        flex: 1;
                        height: 100%;

                        input {
                            font-size: 15px;
                            font-weight: 700;
                            width: 100%;
                            height: 100%;
                            background: transparent;
                            border: none;
                            outline: none;
                            color: #fff;
                        }

                    }

                    .form-item-content-operation {
                        img {
                            width: 16px;
                            height: 16px;
                        }
                    }
                }
            }


        }

        .bind-email-content-code {
            margin-top: 60px;

            &-label {
                font-size: 12px;
                font-weight: 500;
                line-height: 14px;
                color: rgba($color: #fff, $alpha: 1);
                margin-bottom: 8px;
            }

            .bind-email-content-code-desc {
                margin-top: 20px;
                font-size: 12px;
                font-weight: 600;
                line-height: 14px;
                color: rgba($color: #fff, $alpha: .5);

                span {
                    color: $common-color;
                }
            }

        }

        .bind-email-content-desc {
            margin-top: 31px;
            display: flex;
            gap: 4px;
            margin-top: 20px;

            img {
                width: 16px;
                height: 16px;
            }

            span {
                font-size: 12px;
                font-weight: 600;
                line-height: 14px;
                color: $danger-color;
            }
        }
    }

    .bind-email-content-button {
        margin-top: 60px;

        .bind-email-content-button-item {
            width: 100%;
            height: 50px;
            background: #1E1D38;
            border-radius: 10px;
            border: none;
            border-radius: 6px;
            position: relative;
            overflow: hidden;
            color: rgba(255, 255, 255, 0.5);
            font-weight: 700;
        }

        .bind-button-active {
            color: #fff;
            background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 50%, #FF812B 100%);
            background-size: 200% 100%;
            animation: gradientMove 3s ease-in-out infinite;
            // 添加发光效果
            box-shadow:
                0 0 20px rgba(255, 129, 43, 0.3),
                0 0 40px rgba(255, 11, 194, 0.2);
            // 添加内部光晕效果
            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
                animation: shimmer 2.5s infinite;
            }
            // 添加脉冲效果
            &::after {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                width: 0;
                height: 0;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.1);
                transform: translate(-50%, -50%);
                animation: pulse 2s ease-out infinite;
            }
        }
    }
}
</style>