<!--  -->
<template>
    <div class="app-container">
        <headerNav :title="'Blacklist'"></headerNav>
        <div class="main-container">
            <var-list loading-text="loading..." finished-text="" error-text="" :finished="finished"
                v-model:loading="loading" @load="loadMoreData(0)">
                <div class="data-list">

                </div>
                <div class="no-data" v-if="!dataList.length && finished">
                    <img src="@/assets/common/no-mybag-data.png" alt="">
                    <span>The blacklist is empty</span>
                </div>
            </var-list>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import headerNav from '@/components/headerNav/headerNav.vue';

const loading = ref(false)
const finished = ref(false)
const pageData = reactive({
    page: 1,
    pageSize: 20
})
const dataList = ref([])
const loadMoreData = (type?: number) => {
    loading.value = true
    setTimeout(() => {
        loading.value = false
        finished.value = true
    }, 1000)
}
</script>
<style lang="scss" scoped>
.app-container {
    overflow: hidden;

    .main-container {
        padding-top: 10px;
        overflow: auto;

        .no-data {
            margin-top: 88px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            img {
                width: 160px;
                height: 160px;
            }

            span {
                margin-top: 15px;
                font-size: 15px;
                line-height: 18px;
                font-weight: 700;
                color: rgba($color: #fff, $alpha: .5);
            }
        }
    }
}
</style>