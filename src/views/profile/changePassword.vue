<!-- 设置页面 -->
<template>
    <div class="app-container change-password">
        <headerNav title="Change Password"></headerNav>
        <div class="change-password-content">
            
            <div class="change-password-content-title">
                Your password should be at least 6 characters
            </div>
            <div class="form-item-input">
                <div class="form-item-content-input-box">
                    <input v-model="profileInfo.oldPassword" :type="oldPasswordShow ? 'text' : 'password'"
                        class="form-item-content-input">
                </div>
                <div class="form-item-content-operation">
                    <img src="@/assets/common/hidden-password.png" alt="" @click="oldPasswordShow = !oldPasswordShow"
                        v-if="!oldPasswordShow">
                    <img src="@/assets/common/show-password.png" alt="" @click="oldPasswordShow = !oldPasswordShow"
                        v-else>
                </div>
            </div>
            <div class="form-item-input">
                <div class="form-item-content-input-box">
                    <input v-model="profileInfo.newPassword" :type="newPasswordShow ? 'text' : 'password'"
                        class="form-item-content-input">
                </div>
                <div class="form-item-content-operation">
                    <img src="@/assets/common/hidden-password.png" alt="" @click="newPasswordShow = !newPasswordShow"
                        v-if="!newPasswordShow">
                    <img src="@/assets/common/show-password.png" alt="" @click="newPasswordShow = !newPasswordShow"
                        v-else>
                </div>
            </div>
            <div class="change-password-button">
                <button class="change-password-button-item" type="button"
                    :disabled="!profileInfo.oldPassword || !profileInfo.newPassword"
                    :class="{ 'change-password-button-item-active': profileInfo.oldPassword && profileInfo.newPassword }">
                    <span>Done</span>
                </button>
            </div>
            <!--忘记密码-->
            <div class="forget-password" @click="goRouterName('ForgetPassword')">
                Forgot password?
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import headerNav from '@/components/headerNav/headerNav.vue'
import { useRouter } from 'vue-router'
const router = useRouter()
const profileInfo = ref({
    oldPassword: '',
    newPassword: ''
})
const oldPasswordShow = ref(false)
const newPasswordShow = ref(false)
const goRouterName = (name: string) => {
    router.push({
        name: name
    })
}
</script>
<style lang="scss" scoped>
.app-container {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    font-family: var(--font-family-urbanist);

    .change-password-content {
        margin-top: 20px;
        flex: 1;
        padding: 0 15px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;

        &-title {
            margin-bottom: 8px;
            font-size: 12px;
            font-weight: 600;
            line-height: 14px;
            color: #FFFFFF80;
        }

        .form-item-input {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 50px;
            padding: 0 15px;
            background-color: #1E1D38;
            border-radius: 10px;

            .form-item-content-input-box {
                flex: 1;
                height: 100%;

                input {
                    font-size: 15px;
                    font-weight: 700;
                    width: 100%;
                    height: 100%;
                    background-color: transparent;
                    border: none;
                    outline: none;
                    color: #fff;
                }
            }

            .form-item-content-operation {
                img {
                    width: 16px;
                    height: 16px;
                }

                .profile-arrow {
                    width: 24px;
                    height: 24px;
                }
            }
        }

        .change-password-button {
            margin-top: 40px;

            .change-password-button-item {
                width: 100%;
                height: 50px;
                background-color: #1E1D38;
                border-radius: 10px;
                border: none;
                color: rgba($color: #fff, $alpha: .5);
                font-size: 16px;
                font-weight: 700;
                position: relative;
                overflow: hidden;

                &.change-password-button-item-active {
                    color: #fff;
                    // 方案1: 使用 background-size 和 background-position 实现渐变移动
                    background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 50%, #FF812B 100%);
                    background-size: 200% 100%;
                    animation: gradientMove 3s ease-in-out infinite;
                    // 添加发光效果
                    box-shadow:
                        0 0 20px rgba(255, 129, 43, 0.3),
                        0 0 40px rgba(255, 11, 194, 0.2);

                    // 添加内部光晕效果
                    &::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: -100%;
                        width: 100%;
                        height: 100%;
                        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
                        animation: shimmer 2.5s infinite;
                    }

                    // 添加脉冲效果
                    &::after {
                        content: '';
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        width: 0;
                        height: 0;
                        border-radius: 50%;
                        background: rgba(255, 255, 255, 0.1);
                        transform: translate(-50%, -50%);
                        animation: pulse 2s ease-out infinite;
                    }
                }
            }
        }
        .forget-password{
            margin-top: 15px;
            text-align: center;
            line-height: 19px;
            font-size:16px;
            font-weight: 700;
            color: rgba($color: #fff, $alpha: .5);
            text-decoration: underline;
        }
    }
}
</style>