<!-- app-container -->
<template>
    <div class="app-container">
        <headerNav title="Language">
            <template #right>
                <div class="language-switch-content-right">
                    <span>Save</span>
                </div>
            </template>
        </headerNav>
        <div class="language-switch-content">
            <div class="language-switch-content-item"
             v-for="item in languageList" :key="item.value" 
             :class="{ 'language-switch-content-item-active': item.value === selectedLanguage }"
             @click="changeSelectedLanguage(item.value)"
             >
                <div class="language-switch-content-item-left">
                    <span>{{ item.name }}</span>
                </div>
                <img src="@/assets/common/language-success.png" class="language-selected" alt="" v-if="item.value === selectedLanguage">
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import headerNav from '@/components/headerNav/headerNav.vue'
const languageList = ref([
    {
        name: 'Auto',
        value: 'auto'
    },
    {
        name: 'English',
        value: 'en'
    },
    {
        name: 'Chinese',
        value: 'zh'
    }
])
const selectedLanguage = ref('auto')

const changeSelectedLanguage = (value: string) => {
    selectedLanguage.value = value
}
</script>
<style lang="scss" scoped>
.app-container {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    font-family: var(--font-family-urbanist);

    .language-switch-content-right {
        font-size: 16px;
        font-weight: 700;
        line-height: 18px;
        color: $common-color;
    }
    .language-switch-content {
        margin-top:13px;
        padding: 0 15px;
        box-sizing: border-box;
        .language-switch-content-item {
            display: flex;
            align-items: center;
            height: 24px;
            justify-content: space-between;
            font-size: 15px;
            color: #ffffff;
            font-weight: 700;
            line-height: 18px;
            margin-bottom: 20px;
            &.language-switch-content-item-active {
                color: $common-color;
            }
            .language-selected {
                width: 24px;
                height: 24px;
            }
        }
    }
}
</style>