<!-- 选择性别弹窗 -->
<template>
    <div class="delete-account">
        <var-popup position="center" v-model:show="deleteAccountDialogShow"  :safe-area="true"
            :default-style="false" :close-on-click-overlay="false">
            <div class="delete-account-dialog">
                <div class="delete-account-dialog-content">
                    <div class="delete-account-dialog-content-title">
                        Reminder
                    </div>
                    <div class="delete-account-dialog-content-desc">
                        Sure to delete your account? It cannot be recovered.
                    </div>
                    <div class="delete-account-dialog-content-button">
                        <div class="delete-account-dialog-content-button-item" @click="close">
                            <span>Cancel</span>
                        </div>
                        <div class="delete-account-dialog-content-button-item confirm-button" @click="confirm">
                            <span>Delete</span>
                        </div>
                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts">

export default {
    name: 'deleteAccountConfirm',
    props: {
        show: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            columns: [
                Array.from({ length: 82 }, (_, i) => ({
                    text: i + 18,
                    value: i + 18
                }))
            ],
            selectedAge: 18,
            deleteAccountDialogShow: this.show
        };
    },
    watch: {
        show: {
            handler(newVal) {
                this.deleteAccountDialogShow = newVal
            },
            immediate: true
        }
    },
    mounted() {

    },
    methods: {
        close() {
            this.deleteAccountDialogShow = false
            this.$emit('close')
            this.$emit('update:show', false)
        },
        confirm() {
            this.$emit('confirm')
            this.close()
        }
    }
}
</script>
<style lang='scss' scoped>
.delete-account-dialog {
    display: flex;
    justify-content: center;
    font-family: var(--var-font-family-type3);

    .delete-account-dialog-content {
        height: 100%;
        width: 80%;
        padding: 40px 30px;
        border-radius: 16px;
        background: rgb(24, 25, 47);
        &-title{
            font-size: 18px;
            color: #fff;
            line-height: 22px;
            text-align: center;
        }
        &-desc {
            margin-top: 15px;
            padding: 0 20px;
            box-sizing: border-box;
            font-size: 14px;
            font-weight: 500;
            line-height: 17px;
            color: rgba($color: #fff, $alpha: .7);
            text-align: center;
        }

        &-button {
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
            gap:10px;
            .delete-account-dialog-content-button-item {
                flex: 1;
                height: 44px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 6px;
                background: rgba($color: #fff, $alpha: .05);
                border-radius: 8px;
                color: #fff;
                font-size: 16px;
                font-weight: 700; 
                &.confirm-button{
                  color: $danger-color;
                }
            }
        }
    }
}
</style>