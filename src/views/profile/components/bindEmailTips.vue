<!-- 需要绑定邮箱提示 -->
<template>
    <div class="bind-email-tips">
        <var-popup position="bottom" v-model:show="showBindEmailTip" :safe-area="true" :default-style="false"
            @click-overlay="close">
            <div class="bind-email-dialog">
                <span class="bind-email-dialog-title">Account Security Alert</span>
                <img src="@/assets/profile/no-bind-img.png" alt="" class="no-bind-img">
                <span class="bind-email-dialog-desc">We have detected that your email is not yet bound to your
                    account.To enhance security and simplify
                    future logins, please bind your email address now.</span>
                <div class="bind-email-button">
                    <div class="bind-email-button-item" @click="close">
                        <span>Later</span>
                    </div>
                    <div class="bind-email-button-item confirm-button" @click="confirm">
                        <span>Bind Now</span>
                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts">

export default {
    name: 'bindEmailTip',
    props: {
        show: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {

            showBindEmailTip: this.show
        };
    },
    watch: {
        show: {
            handler(newVal) {
                this.showBindEmailTip = newVal
            },
            immediate: true
        }
    },
    mounted() {

    },
    methods: {
        close() {
            this.showBindEmailTip = false
            this.$emit('close')
            this.$emit('update:show', false)
        },
        confirm() {

            this.$emit('confirm')
            this.close()
        }
    }
}
</script>
<style lang='scss' scoped>
.bind-email-dialog {
    display: flex;
    justify-content: center;
    font-family: var(--var-font-family-type3);
    padding: 20px 30px 50px;
    background-color: #0A0A1B;
    border-radius: 16px 16px 0 0;
    display: flex;
    flex-direction: column;
    align-items: center;

    .bind-email-dialog-title {
        font-size: 18px;
        font-weight: 700;
        color: #fff;
        line-height: 22px;
    }

    .no-bind-img {
        width: 160px;
        height: 160px;
        margin: 20px 0 10px;
    }

    .bind-email-dialog-desc {
        font-size: 14px;
        font-weight: 500;
        color: rgba($color: #fff, $alpha: 0.7);
        line-height: 17px;
        text-align: center;
    }

    .bind-email-button {
        width: 100%;
        margin-top: 30px;
        display: flex;
        justify-content: space-between;
        gap: 25px;

        .bind-email-button-item {
            flex: 1;
            height: 44px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 6px;
            background: rgba($color: #fff, $alpha: .05);
            border-radius: 8px;
            color: #fff;
            font-size: 16px;
            font-weight: 700;
            &.confirm-button {
                background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                
            }
        }
    }

}
</style>