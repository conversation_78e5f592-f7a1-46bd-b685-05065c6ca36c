<!-- 选择性别弹窗 -->
<template>
    <div class="select-gender-dialog">
        <var-popup position="bottom" v-model:show="selectGenderDialogShow" @click-overlay="close" :safe-area="true"
            :default-style="false">
            <div class="gender-dialog">
                <div class="select-box-item male" :class="{ 'select-box-item-active': selectGender === 'male' }"
                    @click="selectGenderFunc('male')" v-ripple="{ color: 'rgba(240,240,240,1)' }">
                    <img src="@/assets/profile/male.png" alt="" v-if="selectGender !== 'male'">
                    <img src="@/assets/profile/male_active.png" alt="" v-else>
                    <span>Male</span>
                </div>
                <div class="select-box-item female" :class="{ 'select-box-item-active': selectGender === 'female' }"
                    @click="selectGenderFunc('female')" v-ripple="{ color: 'rgba(240,240,240,1)' }">
                    <img src="@/assets/profile/female.png" alt="" v-if="selectGender !== 'female'">
                    <img src="@/assets/profile/female_active.png" alt="" v-else>
                    <span>Female</span>
                </div>
                <div class="confirm-btn" @click="confirm">
                    OK
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts">
export default {
    name: 'selectGenderDialog',
    props: {
        show: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            selectGender: 'male',
            selectGenderDialogShow: this.show
        };
    },
    watch: {
        show: {
            handler(newVal) {
                this.selectGenderDialogShow = newVal
            },
            immediate: true
        }
    },
    mounted() {

    },
    methods: {
        close() {
            this.selectGenderDialogShow = false
            this.$emit('close')
            this.$emit('update:show', false)
        },
        selectGenderFunc(gender: string) {
            console.log(gender)
            this.selectGender = gender

        },
        confirm() {
            console.log(this.selectGender)
            this.$emit('confirm', this.selectGender)
            this.close()
        }
    }
}
</script>
<style lang='scss' scoped>
.gender-dialog {
    background: rgb(24, 25, 47);
    border-radius: 16px 16px 0 0;
    padding: 20px;
    min-height: 200px;
    font-family: var(--var-font-family-type3);

    .select-box-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: rgb(34, 35, 65);
        border-radius: 17px;
        margin: 0px auto 13px;
        padding: 0px 20px 0px 13px;
        height: 57px;

        span {
            font-size: 16px;
            font-weight: 400;
            line-height: 14px;
            color: #fff;
        }

        img {
            width: 34px;
            height: 34px;
        }

        &.male {
            &.select-box-item-active {
                background: rgb(28, 105, 250);
            }
        }

        &.female {
            &.select-box-item-active {
                background: rgb(255, 27, 168);
            }
        }
    }

    .confirm-btn {
        width: 100%;
        height: 52px;
        font-size: 16px;
        color: #fff;
        text-align: center;
        line-height: 52px;

    }
}
</style>