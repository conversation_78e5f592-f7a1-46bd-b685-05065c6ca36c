<!-- 选择性别弹窗 -->
<template>
    <div class="select-Age-dialog">
        <var-popup position="bottom" v-model:show="selectAgeDialogShow" @click-overlay="close" :safe-area="true"
            :default-style="false">
            <div class="age-dialog">
                <div class="age-dialog-content">
                    <var-picker :cancel-button-text="'Cancel'" :title="''" :confirm-button-text="'OK'"
                        :confirm-button-text-color="'#ff1ba8'" :cancel-button-text-color="'#fff'" :columns="columns"
                        @change="handleChange" :default-index="[0]" class="age-picker" @confirm="confirmAge"
                        @cancel="close" />
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts">

export default {
    name: 'selectAgeDialog',
    props: {
        show: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            columns: [
                Array.from({ length: 82 }, (_, i) => ({
                    text: i + 18,
                    value: i + 18
                }))
            ],
            selectedAge: 18,
            selectAgeDialogShow: this.show
        };
    },
    watch: {
        show: {
            handler(newVal) {
                this.selectAgeDialogShow = newVal
            },
            immediate: true
        }
    },
    mounted() {

    },
    methods: {
        close() {
            this.selectAgeDialogShow = false
            this.$emit('close')
            this.$emit('update:show', false)
        },
        handleChange(value: any) {

            this.selectedAge = value[0]
        },
        confirmAge() {

            this.$emit('confirm', this.selectedAge)
            this.close()
        }
    }
}
</script>
<style lang='scss' scoped>
.age-dialog {
    background: rgb(24, 25, 47);
    border-radius: 16px 16px 0 0;
    padding: 10px 0 0;
    height: 346px;
    font-family: var(--var-font-family-type3);

    .age-dialog-content {
        height: 100%;
    }

    .var-picker {
        height: 100%;
        background-color: transparent;

        :deep(.var-picker__mask) {
            background-image: none;
        }

        :deep(.var-picker__option) {
            height: 52px;
            line-height: 52px;

            .var-picker__text {
                font-size: 22px;
                color: #fff;
            }
        }

        :deep(.var-picker__picked) {
            height: 52px;
            background-color: rgba(104, 139, 200, 0.18);
        }
    }
}
</style>