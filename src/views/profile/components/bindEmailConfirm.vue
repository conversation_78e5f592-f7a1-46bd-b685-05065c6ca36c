<!-- 需要绑定邮箱提示 -->
<template>
    <div class="discount-popup">
        <var-popup position="bottom" v-model:show="showBindEmailTip" :safe-area="true" :default-style="false"
            @click-overlay="close">
            <div class="bind-email-dialog">
               
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts">

export default {
    name: 'bindEmailTip',
    props: {
        show: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {

            showBindEmailTip: this.show
        };
    },
    watch: {
        show: {
            handler(newVal) {
                this.showBindEmailTip = newVal
            },
            immediate: true
        }
    },
    mounted() {

    },
    methods: {
        close() {
            this.showBindEmailTip = false
            this.$emit('close')
            this.$emit('update:show', false)
        },
        confirm() {

            this.$emit('confirm')
            this.close()
        }
    }
}
</script>
<style lang='scss' scoped>
.bind-email-dialog {
    display: flex;
    justify-content: center;
    font-family: var(--var-font-family-type3);
    padding: 20px 30px 50px;
    background-color: #0A0A1B;
    border-radius: 16px 16px 0 0;
    display: flex;
    flex-direction: column;
    align-items: center;

}
</style>