<!-- 设置页面 -->
<template>
    <div class="app-container setting-page">
        <headerNav title="Setting" />
        <div class="profile-menu">
            <!--My Account-->
            <div class="profile-menu-item" @click="goRouterName('Account')">
                <div class="profile-menu-item-left">
                    <img src="@/assets/profile/my-account.png" alt="" class="profile-menu-item-left-icon">
                    <span class="profile-menu-item-left-text">My Account</span>
                </div>
                <div class="profile-menu-item-right">
                    <div class="profile-menu-item-right-desc">
                        <img src="@/assets/profile/account-unprotected.png" alt="">
                        <span>Unprotected</span>
                    </div>
                    <img src="@/assets/profile/profile-arrow.png" alt="" class="profile-menu-item-right-icon">
                </div>
            </div>
            <!--language-->
            <div class="profile-menu-item" @click="goRouterName('LanguageSwitch')">
                <div class="profile-menu-item-left">
                    <img src="@/assets/profile/language.png" alt="" class="profile-menu-item-left-icon">
                    <span class="profile-menu-item-left-text">Language</span>
                </div>
                <div class="profile-menu-item-right">
                    <div class="profile-menu-item-right-language">
                        <span>English</span>
                    </div>
                    <img src="@/assets/profile/profile-arrow.png" alt="" class="profile-menu-item-right-icon">
                </div>
            </div>
            <!--Delete Account-->
            <div class="profile-menu-item" @click="deleteAccountConfirmShow = true">
                <div class="profile-menu-item-left">
                    <img src="@/assets/profile/delete.png" alt="" class="profile-menu-item-left-icon">
                    <span class="profile-menu-item-left-text">Delete Account</span>
                </div>
                <div class="profile-menu-item-right">
                    <img src="@/assets/profile/profile-arrow.png" alt="" class="profile-menu-item-right-icon">
                </div>
            </div>
            <!--Block Strangers’ Messages-->
            <div class="profile-menu-item">
                <div class="profile-menu-item-left">
                    <img src="@/assets/profile/block.png" alt="" class="profile-menu-item-left-icon">
                    <span class="profile-menu-item-left-text">Block Strangers’ Messages</span>
                </div>
                <div class="profile-menu-item-right">
                    <van-switch v-model="blockStrangersMessage" size="20px" active-color="#FF1A9E"
                        inactive-color="#1E1D38" />
                </div>
            </div>
            <!--Network Proxy-->
            <div class="profile-menu-item">
                <div class="profile-menu-item-left">
                    <img src="@/assets/profile/network.png" alt="" class="profile-menu-item-left-icon">
                    <span class="profile-menu-item-left-text">Network Proxy</span>
                </div>
                <div class="profile-menu-item-right">
                    <van-switch v-model="networkProxy" size="20px" active-color="#FF1A9E"
                        inactive-color="#1E1D38" />
                </div>
            </div>
            <button class="common-btn logout-btn" @click="logout">
                Sign out
            </button>
        </div>
        <deleteAccountConfirm :show="deleteAccountConfirmShow" @close="deleteAccountConfirmShow = false"
            @confirm="deleteAccountConfirmShow = false" />
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'


import deleteAccountConfirm from './components/deleteAccountConfirm.vue'
import headerNav from '@/components/headerNav/headerNav.vue'
const router = useRouter()
const blockStrangersMessage = ref(false)
const networkProxy = ref(false)
const deleteAccountConfirmShow = ref(false)
const goRouterName = (name: string) => {
    router.push({
        name: name
    })
}
</script>
<style lang="scss" scoped>
.app-container {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    font-family: var(--font-family-urbanist);

    .common-nav {
        width: 100%;
        height: 44px;
        padding: 0 15px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;

        &-left {
            img {
                width: 24px;
                height: 24px;
            }
        }

        &-title {
            font-size: 18px;
            font-weight: 700;
            line-height: 22px;
            color: #fff;
        }

        &-right {
            font-size: 16px;
            font-weight: 700;
            color: $common-color;
        }
    }

    .profile-menu {
        margin-top: 24px;
        padding: 0 15px;
        box-sizing: border-box;

        &-item {
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;

            &-left {
                display: flex;
                align-items: center;
                gap: 8px;

                &-icon {
                    width: 30px;
                    height: 30px;
                }

                &-text {
                    color: #fff;
                    font-size: 15px;
                    font-weight: 700;
                    line-height: 18px;
                }
            }

            &-right {
                display: flex;
                align-items: center;
                gap: 4px;

                &-icon {
                    width: 24px;
                    height: 24px;
                }

                .enter-effect {
                    width: 44px;
                    height: 16px;
                }

                &-desc {
                    display: flex;
                    align-items: center;
                    font-size: 15px;
                    color: #FFD016;
                    font-weight: 700;

                    img {
                        width: 20px;
                        height: 20px;
                    }
                }

                &-language {
                    display: flex;
                    align-items: center;
                    font-size: 15px;
                    font-weight: 700;
                    color: rgba($color: #fff, $alpha: 0.5);
                }
            }
        }
        .logout-btn{
            margin-top: 30px;
            width: 100%;
            height: 44px;
            border-radius: 6px;
            background: #1E1D38;
            color: #fff;
            outline: none;
            border: none;
            font-size: 16px;
            font-weight: 700;
        }
    }
}
</style>