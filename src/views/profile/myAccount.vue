<!-- 设置页面 -->
<template>
    <div class="app-container account-page">
        <headerNav title="My Account"></headerNav>
        <div class="account-page-content">
            <div class="account-page-content-header">
                <img src="@/assets/profile/no-bind-img.png" alt="" v-if="!emailHasBind">
                <img src="@/assets/profile/email-has-bind.png" alt="" v-else>
                <span v-if="!emailHasBind">Your account lacks an email binding and password.
                    To safeguard your coins and ensure easy future
                    logins, please bind your email now.</span>
                <span v-else class="has-bind-text">Your Account is Protected</span>
            </div>
            <!--language-->
            <div class="profile-menu-item" @click="goRouterName('BindEmail')">
                <div class="profile-menu-item-left">
                    <span class="profile-menu-item-left-text">Email</span>
                </div>
                <div class="profile-menu-item-right">
                    <div class="profile-menu-item-right-language">
                        <span>Bind</span>
                    </div>
                    <img src="@/assets/profile/profile-arrow.png" alt="" class="profile-menu-item-right-icon">
                </div>
            </div>
            <div class="profile-menu-item" @click="goRouterName('ChangePassword')" v-if="emailHasBind">
                <div class="profile-menu-item-left">
                    <span class="profile-menu-item-left-text">Change Password</span>
                </div>
                <div class="profile-menu-item-right">
                    <img src="@/assets/profile/profile-arrow.png" alt="" class="profile-menu-item-right-icon">
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import headerNav from '@/components/headerNav/headerNav.vue'
import { useRouter } from 'vue-router'
const router = useRouter()

const emailHasBind = ref(true)
const goRouterName = (name: string) => {
    router.push({
        name: name
    })
}
</script>
<style lang="scss" scoped>
.app-container {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    font-family: var(--font-family-urbanist);

    .account-page-content {
        margin-top: 20px;
        flex: 1;
        padding: 0 15px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;

        &-header {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0 14px 20px;
            box-sizing: border-box;
            gap: 20px;
            border-bottom: 1px solid #FFFFFF66;

            img {
                width: 160px;
                height: 160px;
            }

            span {
                font-size: 14px;
                font-weight: 500;
                line-height: 17px;
                color: rgba($color: #fff, $alpha: 0.7);
                text-align: center;
                &.has-bind-text{
                    color: #fff;
                }
            }
        }

        .profile-menu-item {
            width: 100%;
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
            &-left {
                display: flex;
                align-items: center;
                gap: 8px;

                &-icon {
                    width: 30px;
                    height: 30px;
                }

                &-text {
                    color: #fff;
                    font-size: 15px;
                    font-weight: 700;
                    line-height: 18px;
                }
            }

            &-right {
                display: flex;
                align-items: center;
                gap: 4px;

                &-icon {
                    width: 24px;
                    height: 24px;
                }

                .enter-effect {
                    width: 44px;
                    height: 16px;
                }

                &-desc {
                    display: flex;
                    align-items: center;
                    font-size: 15px;
                    color: #FFD016;
                    font-weight: 700;

                    img {
                        width: 20px;
                        height: 20px;
                    }
                }

                &-language {
                    display: flex;
                    align-items: center;
                    font-size: 15px;
                    font-weight: 700;
                    color: rgba($color: #fff, $alpha: 0.5);
                }
            }
        }
    }
}
</style>