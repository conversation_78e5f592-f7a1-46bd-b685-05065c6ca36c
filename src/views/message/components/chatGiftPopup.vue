<!--  -->
<template>
    <div>
        <var-popup position="bottom" v-model:show="showGiftPopup" @click-overlay="close" :safe-area="true"
            :default-style="false">
            <div class="gift-dialog">
                <div class="gift-dialog-header">
                    <div class="gift-dialog-header-common">
                        <div class="gift-dialog-header-common-left">

                        </div>
                        <div class="gift-dialog-header-common-center">
                            <div class="center-top">

                            </div>
                            <div class="center-bottom">
                                Gift &nbsp;<span>+2</span>&nbsp; EXP
                            </div>
                        </div>
                        <img src="@/assets/common/arrow-right.png" alt="" class="gift-dialog-header-common-right">
                    </div>
                    <div class="gift-dialog-header-svip" v-if="activeTab === 1">
                    </div>
                </div>
                <!--下面是礼物部分-->
                <div class="gift-dialog-content">
                    <div class="gift-dialog-content-top">
                        <div class="gift-dialog-content-top-left">
                            <div class="gift-tab-item" v-for="(value, index) in tabsList" :key="value.value"
                                :class="{ 'gift-tab-item-active': activeTab === value.value }"
                                @click="handleTabChange(index)">
                                {{ value.label }}
                                <div class="new-tag" v-if="value.isNew">
                                    New
                                </div>
                            </div>
                        </div>
                        <div class="gift-dialog-content-top-right">
                            <img src="@/assets/gift/gift-backpack.png" alt="">
                        </div>
                    </div>
                    <div class="gift-dialog-content-center">
                        <var-swipe ref="mySwipe" class="swipe-example" :loop="false" :indicator="false" @change="handleSwipeChange">
                            <var-swipe-item v-for="value in tabsList" :key="'swiper' + '_' + value.value"
                                :swipe-item-style="{ height: '100%' }">
                                <var-list :loading-text="''" :finished="value.finished" v-model:loading="value.loading"
                                    @load="loadGiftData" :immediate-check="false">
                                    <div class="gift-dialog-content-center-list">
                                        <div class="gift-dialog-content-center-item" v-for="item in value.dataList"
                                            :key="item.id">
                                            <div class="gift-img-container">
                                                <!-- 占位符 -->
                                                <div class="gift-img-placeholder" v-if="!item.isLoaded">
                                                    <div class="placeholder-spinner"></div>
                                                </div>
                                                <!-- 真实图片 -->
                                                <img :src="item.img" 
                                                     :alt="item.name" 
                                                     class="gift-img"
                                                     :class="{ 'gift-img-loaded': item.isLoaded }"
                                                     @load="onImageLoad(item.id)"
                                                     @error="onImageError(item.id)">
                                            </div>
                                            <div class="gift-dialog-content-center-item-name">
                                                {{ item.name }}
                                            </div>
                                            <div class="gift-dialog-content-center-item-price">
                                                <img src="@/assets/common/coins.png" alt="" class="coins-img">
                                                <span class="coins-num">20</span>
                                            </div>
                                        </div>
                                    </div>

                                </var-list>
                            </var-swipe-item>
                        </var-swipe>
                    </div>
                </div>
                <!--个人金币部分-->
                <div class="gift-dialog-bottom">
                    <div class="bottom-left">
                        <img src="@/assets/common/coins.png" alt="" class="coins-img" @click="changeCoinsNotEnoughPopup">
                        <span class="coins-num" @click="changeCoinsNotEnoughPopup">20</span>
                        <img src="@/assets/common/arrow-right.png" alt="" class="arrow-right" >
                        <img src="@/assets/anchor/gift_icon.gif" alt="" class="gift-icon" @click="changeShowDiscountPopup">
                    </div>
                    <div class="bottom-right">
                        <var-menu v-model:show="showGitNumMenu" @close="showGitNumMenu = false" same-width
                            trigger="click" offset-y="25" :default-style="false" popover-class="git-num-menu">
                            <div class="bottom-right-num" @click.stop="showGitNumMenu = !showGitNumMenu">
                                {{ sendGiftNum }}
                            </div>
                            <template #menu>
                                <div class="menu-item" v-for="value in gitNumList" :key="value.value"
                                    @click="handleGitNum(value.value)">
                                    {{ value.label }}
                                </div>
                            </template>
                        </var-menu>
                        <img src="@/assets/common/arrow-up.png" alt="" class="arrow-up"
                            :class="{ 'arrow-up-active': showGitNumMenu }">
                        <button class="send-button"> Send </button>
                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts" setup name="ChatGiftPopup">
import { ref, watch, onMounted, nextTick } from 'vue'

interface Props {
    show: boolean
}
const emit = defineEmits(['update:show','changeShowDiscountPopup','changeCoinsNotEnoughPopup'])
const showGiftPopup = ref(false)
const showGitNumMenu = ref(false)

const mySwipe = ref<any>(null)
const tabsList = ref([{
    label: 'Popular',
    value: 0,
    pageSize: 20,
    pageNum: 1,
    finished: false,
    loading: false,
    dataList: [] as any[]
}, {
    label: 'SVIP',
    value: 1,
    isNew: true,
    pageSize: 20,
    pageNum: 1,
    finished: false,
    loading: false,
    dataList: [] as any[]
}, {
    label: 'Fun',
    value: 2,
    isNew: true,
    pageSize: 20,
    pageNum: 1,
    finished: false,
    loading: false,
    dataList: [] as any[]
}, {
    label: 'Costomized',
    value: 3,
    pageSize: 20,
    pageNum: 1,
    finished: false,
    loading: false,
    dataList: [] as any[]

}, {
    label: 'User lv',
    value: 4,
    pageSize: 20,
    pageNum: 1,
    finished: false,
    loading: false,
    dataList: [] as any[]
}])
const gitNumList = ref([{
    label: 'other',
    value: 0
}, {
    label: '500',
    value: 500
}, {
    label: '200',
    value: 200
},
{
    label: '100',
    value: 100
},
{
    label: '50',
    value: 50
},
{
    label: '30',
    value: 30
},
{
    label: '10',
    value: 10
},
{
    label: '1',
    value: 1
}
])
const activeTab = ref(0)
const props = defineProps<Props>()
const sendGiftNum = ref(1)


// 图片加载状态处理
const onImageLoad = (itemId: number) => {
    const currentTab = tabsList.value[activeTab.value]
    const item = currentTab.dataList.find((item: any) => item.id === itemId)
    if (item) {
        item.isLoaded = true
        item.isError = false
    }
}

const onImageError = (itemId: number) => {
    const currentTab = tabsList.value[activeTab.value]
    const item = currentTab.dataList.find((item: any) => item.id === itemId)
    if (item) {
        item.isError = true
        item.isLoaded = false
    }
}

watch(() => showGiftPopup.value, (newVal: boolean) => {
    emit('update:show', newVal)
})

const handleGitNum = (value: number) => {
    sendGiftNum.value = value
    showGitNumMenu.value = false
}

const loadGiftData = () => {
    if (!tabsList.value[activeTab.value].dataList.length && !tabsList.value[activeTab.value].finished) {
        console.log(1111)
        setTimeout(() => {
            const newData = [
                {
                    id: 1,
                    name: 'Gift 1',
                    img: 'https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/17267390551695800715Kumars_Cucumber.gif',
                    isLoaded: false,
                    isError: false
                },
                {
                    id: 2,
                    name: 'Gift 2',
                    img: 'https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/1752749971JohnnyBeGood.png',
                    isLoaded: false,
                    isError: false
                },
                {
                    id: 3,
                    name: 'Gift 3',
                    img: 'https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/1752749981Akira.png',
                    isLoaded: false,
                    isError: false
                },
                {
                    id: 4,
                    name: 'Gift 4',
                    img: 'https://d2sbw7ntmjyyf3.cloudfront.net/apps/img/1749003069Rosy.png',
                    isLoaded: false,
                    isError: false
                },
            ]
           console.log(activeTab.value) 
            tabsList.value[activeTab.value].dataList = tabsList.value[activeTab.value].dataList.concat(newData)
            tabsList.value[activeTab.value].finished = true
            tabsList.value[activeTab.value].loading = false

            console.log(tabsList.value[activeTab.value].dataList)
        }, 1000)
    }
}

const handleSwipeChange = (index: number) => {
    activeTab.value = index
    loadGiftData()
}

const handleTabChange = (index: number) => {
    activeTab.value = index
    mySwipe.value?.to(index)
}

watch(() => props.show, (newVal: boolean) => {
    showGiftPopup.value = newVal
    if (newVal) {
        tabsList.value[activeTab.value].finished = false
        tabsList.value[activeTab.value].loading = false
        tabsList.value[activeTab.value].dataList = []
        loadGiftData()
    }
}, {
    immediate: true
})

/**
 * Close the popup.
 */
const close = () => {
    showGiftPopup.value = false
}

const changeShowDiscountPopup = () => {
    emit('changeShowDiscountPopup')
}

const changeCoinsNotEnoughPopup = () => {
    emit('changeCoinsNotEnoughPopup')
}
</script>
<style lang="scss" scoped>
.gift-dialog {
    width: 100%;
    height: 394px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    font-family: var(--font-family-urbanist);

    &-header {
        width: 100%;
        height: 50px;

        &-common {
            width: 100%;
            height: 100%;
            background-color: #1E1D38;
            border-radius: 16px 16px 0 0;
            padding: 0 15px;
            display: flex;
            align-items: center;

            &-left {
                width: 50px;
                height: 20px;
                background: url('@/assets/common/vip-level.png') no-repeat center center;
                background-size: 100% 100%;
                margin-right: 5px;
            }

            &-center {
                flex: 1;
                margin-right: 10px;

                .center-top {
                    width: 100%;
                    height: 6px;
                    background: rgba($color: #fff, $alpha: .2);
                    border-radius: 3px;
                }

                .center-bottom {
                    margin-top: 7px;
                    font-size: 12px;
                    font-weight: 600;
                    color: rgba($color: #fff, $alpha: .5);
                    line-height: 14px;

                    span {
                        color: #FFBD10;
                    }
                }
            }

            &-right {
                width: 24px;
                height: 24px;
            }
        }
    }

    &-content {
        padding: 0 15px 0;
        box-sizing: border-box;
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        background-color: #1E1D38;

        &-top {
            width: 100%;

            display: flex;
            align-items: center;
            justify-content: space-between;

            &-left {

                flex: 1;
                display: flex;
                align-items: center;
                overflow-x: scroll;
                font-size: 15px;
                font-weight: 700;
                color: rgba($color: #fff, $alpha: .5);
                gap: 20px;

                .gift-tab-item {
                    flex-shrink: 0;
                    position: relative;
                    height: 40px;
                    line-height: 40px;
                    &.gift-tab-item-active {
                        color: #fff;
                    }
                    .new-tag{
                        position: absolute;
                        padding: 0 4px;
                        background-color: $common-color;
                        border-radius: 4px 4px 4px 0;
                        font-size: 10px;
                        font-weight: 700;
                        line-height: 12px;
                        color: #fff;
                        top: 2px;
                        left: 12px;
                        z-index: 100;
                    }
                }
            }
            &-right {
                width: 24px;
                height: 24px;

                img {
                    width: 100%;
                    height: 100%;
                }
            }
        }

        &-center {
            width: 100%;
            flex: 1;

            .var-swipe {
                width: 100%;
                height: 100%;

                .var-swipe-item {
                    overflow-y: auto;

                    .var-list {
                        width: 100%;
                        height: 100%;

                        .gift-dialog-content-center-list {
                            width: 100%;
                            height: 100%;
                            display: grid;
                            grid-template-columns: repeat(4, 1fr);
                            gap: 10px;

                            .gift-dialog-content-center-item {
                                width: 100%;
                                height: 100px;
                                display: flex;
                                flex-direction: column;
                                align-items: center;

                                .gift-img-container {
                                    position: relative;
                                    width: 60px;
                                    height: 60px;
                                    border-radius: 8px;
                                    overflow: hidden;
                                    background-color: #2A294A;
                                }

                                .gift-img {
                                    width: 100%;
                                    height: 100%;
                                    object-fit: cover;
                                    opacity: 0;
                                    transition: opacity 0.3s ease-in-out;

                                    &.gift-img-loaded {
                                        opacity: 1;
                                    }
                                }

                                .gift-img-placeholder {
                                    position: absolute;
                                    top: 0;
                                    left: 0;
                                    width: 100%;
                                    height: 100%;
                                    display: flex;
                                    justify-content: center;
                                    align-items: center;
                                    background-color: #2A294A;
                                    border-radius: 8px;
                                    z-index: 1;
                                }

                                .placeholder-spinner {
                                    width: 20px;
                                    height: 20px;
                                    border: 2px solid rgba($color: #fff, $alpha: .3);
                                    border-top: 2px solid #fff;
                                    border-radius: 50%;
                                    animation: spin 1s linear infinite;
                                }

                                @keyframes spin {
                                    0% { transform: rotate(0deg); }
                                    100% { transform: rotate(360deg); }
                                }

                                .gift-dialog-content-center-item-name {
                                    margin-top: 4px;
                                    font-size: 12px;
                                    font-weight: 600;
                                    line-height: 14px;
                                    color: #fff;
                                }

                                .gift-dialog-content-center-item-price {
                                    margin-top: 5px;
                                    display: flex;
                                    align-items: center;
                                    gap: 2px;

                                    .coins-img {
                                        width: 12px;
                                        height: 12px;
                                    }

                                    .coins-num {
                                        font-size: 10px;
                                        font-weight: 700;
                                        line-height: 12px;
                                        color: rgba($color: #fff, $alpha: .7);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    &-bottom {
        padding: 0 15px 29px;
        background-color: #1E1D38;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;

        .bottom-left {
            display: flex;
            align-items: center;

            .coins-img {
                width: 20px;
                height: 20px;
                margin-right: 3px;
            }

            .coins-num {
                font-size: 16px;
                color: #fff;
                font-weight: 700px;
                margin-right: 2px;
            }

            .arrow-right {
                width: 20px;
                height: 20px;
                margin-right: 2px;
            }

            .gift-icon {
                width: 78px;
                height: 32px;
            }
        }

        .bottom-right {
            width: 120px;
            height: 30px;
            border: 1px solid rgba($color: #fff, $alpha: .2);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            :deep(.var-menu) {
                flex: 1;
                font-size: 12px;
                color: #fff;
                font-weight: 400px;
                text-align: center;

                .bottom-right-num {
                    width: 100%;
                    text-align: center;
                }
            }

            .arrow-up {
                width: 12px;
                height: 12px;
                margin-right: 10px;

                &.arrow-up-active {
                    transform: rotate(180deg);
                    transition: all 0.3s ease-in-out;
                }
            }

            .send-button {
                height: 100%;
                padding: 0 16px;
                box-sizing: border-box;
                border-radius: 10px;
                border: none;
                background: linear-gradient(90deg, #692AFF 0%, #28A8FF 100%);
                font-size: 12px;
                color: #fff;

            }
        }
    }
}
</style>
<style>
.git-num-menu {
    display: flex;
    flex-direction: column;
    background-color: #1E1D38;
    color: #E2D5D1;
    width: 64px !important;
    line-height: 28px;
    font-size: 12px;
    text-align: center;
    color: #E2D5D1;
    border: 1px solid #585a5c;
    border-radius: 10px;
}
</style>