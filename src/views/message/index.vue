<!--  -->
<template>
    <div class="app-container message-container">
        <!-- 顶部导航栏 -->
        <div class="header">
            <div class="tab-titles">
                <div v-for="(tab, index) in tabs" :key="tab.name" class="tab-title"
                    :class="{ active: activeTab === index }" @click="switchTab(index)">
                    {{ tab.title }}
                </div>
            </div>
            <div class="header-actions">
                <div class="search-icon"><img src="@/assets/message/clear.png" alt=""></div>
                <div class="trophy-icon"><img src="@/assets/message/delete.png" alt="" @click="deleteMessage"></div>
            </div>
        </div>
        <!-- Swiper 容器 -->
        <div class="swiper-container">
            <var-swipe ref="mySwipe" @change="onSwipeChange" :loop="false" class="mySwipe" :indicator="false">
                <var-swipe-item>
                    <!-- <PullRefresh v-model="refreshLoading[0]" @refresh="onRefresh(0)" :threshold="60"
                        :success-duration="1500"> -->
                    <var-list loading-text="loading..." finished-text="" error-text="" :finished="tabs[0].finished"
                        v-model:loading="tabs[0].loading" @load="load(0)">
                        <div class="slide-content">
                            <div class="content-wrapper">
                                <div class="message-list">
                                    <div class="message-item" v-for="item in 10" :key="item">
                                        <div class="message-item-left">
                                            <div class="message-item-left-delete" v-if="isDelete">
                                                <img src="@/assets/message/deleteUnselect.png" alt=""
                                                    v-if="!deleteList.includes(item)"
                                                    @click="changeDeleteList('add', item)">
                                                <img src="@/assets/message/deleteSelected.png" alt="" v-else
                                                    @click="changeDeleteList('remove', item)">
                                            </div>
                                            <div class="message-item-avatar">
                                                <img src="@/assets/default.png" alt="">
                                            </div>
                                            <div class="message-item-content">
                                                <div class="message-item-content-title">
                                                    <span class="name">SAYRA💎</span>
                                                    <img src="@/assets/message/official.png" alt=""
                                                        class="official-icon">
                                                </div>
                                                <div class="message-item-content-text">
                                                    <span>Welcome! Enjoy your time hWelcome! Enjoy your time
                                                        hWelcome! Enjoy your time h</span>
                                                </div>
                                            </div>
                                        </div>
                                        <!--右边时间以及未读消息显示-->
                                        <div class="message-item-right">
                                            <div class="message-item-right-time">
                                                <span>12:00</span>
                                            </div>
                                            <div class="message-item-right-unread">
                                                10
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </var-list>
                    <!-- </PullRefresh> -->
                </var-swipe-item>

                <!-- Hot 标签页 -->
                <var-swipe-item>
                    <!-- <PullRefresh v-model="refreshLoading[1]" @refresh="onRefresh(1)" :threshold="60"
                        :success-duration="1500"> -->
                    <div class="slide-content call-slide-content">
                        <div class="content-wrapper call-content-wrapper">
                            <!--下面的三个tab-->

                            <div class="call-wrapper-header">
                                <div class="call-wrapper-header-item" v-for="(item) in callTabList" :key="item.value"
                                    :class="{ 'call-tab-active': callTabActive === item.value }">
                                    {{ item.label }}
                                </div>
                                <!-- 内容 -->
                            </div>
                            <div class="call-wrapper-content">
                                <var-swipe class="call-wrapper-content-swipe" ref="callSwipe" :loop="false"
                                    :indicator="false" @touchstart.stop @touchmove.stop @touchend.stop>
                                    <var-swipe-item>
                                        <var-list loading-text="loading..." finished-text="" error-text=""
                                            :finished="tabs[1].finished" v-model:loading="tabs[1].loading"
                                            @load="load(1)">
                                            11111111
                                        </var-list>
                                    </var-swipe-item>
                                    <var-swipe-item>
                                        <var-list loading-text="loading..." finished-text="" error-text=""
                                            :finished="tabs[1].finished" v-model:loading="tabs[1].loading"
                                            @load="load(1)">
                                            111111
                                        </var-list>
                                    </var-swipe-item>
                                </var-swipe>
                            </div>
                        </div>
                    </div>
                    <!-- </PullRefresh> -->
                </var-swipe-item>
            </var-swipe>
            <div class="message-footer" v-if="isDelete">
                <div class="message-footer-btn-left">
                    <img src="@/assets/message/deleteSelected.png" alt="" v-if="deleteList.length === 10"
                        @click="deleteList = []">
                    <img src="@/assets/message/deleteUnselect.png" alt="" v-else @click="">
                    <span>All</span>
                </div>
                <div class="message-footer-btn-right">
                    <button class="common-button cancal-button" @click="changeDeleteList('removeAll')">Cancel</button>
                    <button class="common-button delete-button">Delete</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
const tabs = ref([
    { name: 'message', title: 'Message', page: 1, pageSize: 10, loading: false, finished: false, dataList: [] as LiveItem[] },
    { name: 'call', title: 'Call', page: 1, pageSize: 10, loading: false, finished: false, dataList: [] as any[] },
])
const activeTab = ref(0)
const mySwipe = ref<any>(null)
// 下拉刷新状态管理
const refreshLoading = ref([false, false])

const allList = ref([])
const connectdList = ref([])
const matchList = ref([])

const paddingBottom = ref(`calc(48px + env(safe-area-inset-bottom))`)
const isDelete = ref(false)
const deleteList = ref([])
const callTabActive = ref('all')
const callTabList = [
    {
        value: 'all',
        label: 'All'
    },
    {
        value: 'connectd',
        label: 'Connectd'
    },
    {
        value: 'Match',
        label: 'Match'
    }
]

watch(isDelete, (newVal) => {
    if (newVal) {
        paddingBottom.value = `calc(96px + env(safe-area-inset-bottom))`
    } else {
        paddingBottom.value = `calc(48px + env(safe-area-inset-bottom))`
    }
})

const switchTab = (index: number) => {
    activeTab.value = index
    mySwipe.value?.to(index)
}
// Swipe 事件处理
const onSwipeChange = (index: number) => {
    activeTab.value = index

}

// 下拉刷新处理
const onRefresh = async (index: number) => {
    refreshLoading.value[index] = true
    console.log(`刷新 ${tabs.value[index].title} 页面`)

    // 模拟异步请求
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 这里可以添加具体的刷新逻辑
    // 例如：重新获取数据、更新内容等

    // 刷新完成后，显示成功状态
    refreshLoading.value[index] = false

    console.log(`${tabs.value[index].title} 页面刷新完成`)
}
// 上拉加载处理
const load = async (index: number) => {
    tabs.value[index].loading = true
    if (index === 0) {
        await new Promise(resolve => setTimeout(() => {
            tabs.value[0].dataList.push(...tabs.value[0].dataList)
            tabs.value[0].loading = false
            tabs.value[0].finished = true
            resolve(true)
        }, 1000))
    }
}

const deleteMessage = () => {
    isDelete.value = !isDelete.value
}
const changeDeleteList = (type: string, item?: any) => {
    if (type === 'add') {
        deleteList.value.push(item)
    } else {
        deleteList.value = deleteList.value.filter(i => i !== item)
    }
    if (type === 'removeAll') {
        deleteList.value = []
        isDelete.value = false
    }
}
</script>
<style lang="scss" scoped>
.message-container {
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.header {
    padding: 15px 15px;
    position: sticky;
    top: 0;
    z-index: 100;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .tab-titles {
        display: flex;
        gap: 15px;

        .tab-title {
            padding: 8px 0;
            font-size: 18px;
            font-weight: 600;
            line-height: 21px;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;

            &.active {
                color: #fff;
                font-weight: 600;

                &::after {
                    content: '';
                    position: absolute;
                    bottom: -5px;
                    left: 0;
                    right: 0;
                    margin: 0 auto;
                    width: 10px;
                    height: 3px;
                    background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                    border-radius: 2px;
                }
            }
        }
    }

    .header-actions {
        display: flex;
        gap: 15px;

        img {
            width: 24px;
            height: 24px;
        }
    }
}

.swiper-container {
    flex: 1;
    overflow: hidden;
    padding-bottom: v-bind(paddingBottom);

    .mySwipe {
        height: 100%;


        .slide-content {
            height: 100%;

            overflow-y: auto;

            .content-wrapper {
                height: 100%;
                padding: 0px 15px;
                margin: 0 auto;
                margin-top: 10px;

            }
        }
    }
}

:deep(.var-list) {
    height: 100%
}

.message-list {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .message-item {
        display: flex;
        justify-content: space-between;

        &-left {
            display: flex;
            // align-items: center;
            gap: 10px;
            flex: 1;
            overflow: hidden;

            .message-item-left-delete {
                width: 18px;
                height: 100%;
                flex-shrink: 0;
                display: flex;
                align-items: center;
                justify-content: center;

                img {
                    width: 18px;
                    height: 18px;
                }
            }

            .message-item-avatar {
                width: 60px;
                height: 60px;
                flex-shrink: 0;

                img {
                    width: 100%;
                    height: 100%;
                    border-radius: 50%;
                }
            }

            .message-item-content {
                display: flex;
                flex-direction: column;
                gap: 10px;
                overflow: hidden;

                .message-item-content-title {
                    display: flex;
                    align-items: center;

                    .name {
                        font-size: 16px;
                        font-weight: 700;
                        line-height: 19px;
                        color: #fff;
                    }

                    .official-icon {
                        height: 16px;
                    }
                }

                .message-item-content-text {
                    width: 100%;
                    font-size: 14px;
                    font-weight: 500;
                    color: rgba(255, 255, 255, 0.7);
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;

                }
            }
        }

        &-right {
            margin-left: 40px;
            flex-shrink: 0;
            text-align: end;

            .message-item-right-time {
                font-size: 12px;
                font-weight: 600;
                color: #999;
            }

            .message-item-right-unread {
                padding: 2px 6px;
                background-color: $danger-color;
                border-radius: 83px;
                text-align: center;
                color: #fff;
                font-size: 12px;
                font-weight: 600;
                line-height: 14px;
                margin-top: 10px;
            }
        }
    }
}

.call-slide-content {
    overflow-y: hidden !important;

    .call-content-wrapper {
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }
}

.call-wrapper {
    height: 100%;
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    &-header {
        display: flex;
        gap: 10px;

        &-item {
            padding: 8px 18px;
            background-color: #1E1D38;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            color: #fff;

            &.call-tab-active {
                background-color: #fff;
                color: #0A0A1B;
            }
        }
    }

    &-content {
        margin-top: 12px;
        flex: 1;
        overflow: auto;

        :deep(.call-wrapper-content-swipe) {
            height: 100%;
        }
    }
}

.message-footer {
    position: fixed;
    bottom: calc(48px + env(safe-area-inset-bottom));
    left: 0;
    right: 0;
    background-color: #000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;

    .message-footer-btn-left {
        display: flex;
        align-items: center;
        gap: 6px;

        img {
            width: 18px;
            height: 18px;
        }

        span {
            font-weight: 700;
            font-size: 16px;
            line-height: 19px;
            color: rgba(255, 255, 255, 0.7);
        }
    }

    .message-footer-btn-right {
        display: flex;
        align-items: center;
        gap: 10px;

        .common-button {
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            line-height: 17px;
            color: #fff;
            border: none;

            &.cancal-button {
                background-color: #1E1D38;
            }

            &.delete-button {
                background-color: $danger-color;
            }
        }
    }
}
</style>