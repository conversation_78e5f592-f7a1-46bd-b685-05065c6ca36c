<!-- 获取svip -->
<template>
    <div class="app-container get-svip-container">
        <headerNav title="Get Svip" />
        <div class="get-svip-content">
            <div class="get-svip-content-user">
                <div class="get-svip-content-user-avatar">
                    <img src="@/assets/default.png" alt="" class="get-svip-content-user-avatar-img">
                    <div class="get-svip-content-user-avatar-name">
                        <span class="name">Visitor-22323</span>
                        <span class="svip">You are not a SVIP user yet</span>
                    </div>
                </div>
            </div>
            <!--join& enjoy-->
            <div class="get-svip-content-join-enjoy">
                Join & Enjoy benefits!
            </div>
            <!--Premium-->
            <div class="get-svip-content-premium">
                <img src="@/assets/memberShip/left.png" alt="" class="left-img">
                <span class="premium-text">Premium Privileges</span>
                <img src="@/assets/memberShip/left.png" alt="" class="right-img">
            </div>
            <!--svip 的部分--->
            <div class="get-svip-content-store">
                <div class="get-svip-content-store-item" v-for="item in 10" :key="item">
                    <img src="https://iulia.iwlive.club/webapp/aso/pro/@1.0.6p/png/rights_04-4ff647e7.png" alt="">
                </div>
            </div>
            <!--concat us -->
            <div class="concat-us">
                <img src="@/assets/common/customer-concat.png" alt="">
                <span class="concat-us-text">Contact Us</span>
                <span class="concat-us-text-desc">if you have questions</span>
            </div>
            <!--bottom-btn-->
            <div class="bottom-btn">
                <div class="bottom-btn-item">
                    <div class="bottom-btn-item-left">
                        $19.99 /Mo
                    </div>
                    <div class="bottom-btn-item-right">
                        Get Now
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import headerNav from '@/components/headerNav/headerNav.vue'
</script>
<style lang="scss" scoped>
.get-svip-container {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    font-family: var(--font-family-urbanist);

    .get-svip-content {
        padding-top: 11px;
        flex: 1;
        padding: 0 15px;
        box-sizing: border-box;
        overflow-y: auto;
        padding-bottom: 147px;

        .get-svip-content-user {
            display: flex;
            align-items: center;

            .get-svip-content-user-avatar {
                display: flex;
                align-items: center;
                gap: 10px;

                .get-svip-content-user-avatar-img {
                    width: 50px;
                    height: 50px;
                    border-radius: 50%;
                }

                .get-svip-content-user-avatar-name {
                    display: flex;
                    flex-direction: column;
                    gap: 4px;

                    .name {
                        font-size: 16px;
                        font-weight: 700;
                        color: #fff;
                    }

                    .svip {
                        font-size: 12px;
                        font-weight: 600;
                        color: rgba($color: #fff, $alpha: .5);
                    }
                }
            }
        }

        .get-svip-content-join-enjoy {
            margin-top: 25px;
            font-size: 12px;
            font-weight: 600;
            color: rgba($color: #fff, $alpha: .5);
            line-height: 14px;
            text-align: center;
        }

        .get-svip-content-premium {
            margin-top: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;

            .left-img {
                width: 62px;
                height: 12px;
            }

            .premium-text {
                font-size: 18px;
                font-weight: 900;
                color: #fff;
                font-style: italic;
                line-height: 22px;
            }

            .right-img {
                width: 62px;
                height: 12px;
                transform: rotate(180deg);
            }
        }

        .get-svip-content-store {
            margin-top: 27px;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;

            &-item {
                width: 100%;
                height: 196px;
                border-radius: 10px;

                img {
                    width: 100%;
                    height: 100%;
                }
            }
        }

        .concat-us {
            margin-top: 33px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            font-size: 12px;
            font-weight: 600;
            color: #fff;
            img {
                width: 20px;
                height: 20px;
            }
            .concat-us-text {
                color: $common-color;
                text-decoration: underline;
            }
        }
        .bottom-btn{
            position: fixed;
            bottom: calc(env(safe-area-inset-bottom) + 54px);
            left: 0;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 23px;
            box-sizing: border-box;
            .bottom-btn-item{
                width: 100%;
                padding: 15px 20px;
                background: linear-gradient(163.19deg, #FBEECB 1.55%, #FBF5E5 49.7%, #F6CD96 78.14%, #EDBEC4 96.94%);
                border-radius: 16px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                .bottom-btn-item-left{
                    font-size: 20px;
                    font-weight: 700;
                    color: #000;
                }
                .bottom-btn-item-right{
                    padding: 6px 12px;
                    font-size: 15px;
                    font-weight: 700;
                    line-height: 18px;
                    color: #FFE39F;
                    background: linear-gradient(90deg, #574F3A 0%, #15120A 100%);
                    border-radius: 20px;
                }
            }
        }
    }
}
</style>