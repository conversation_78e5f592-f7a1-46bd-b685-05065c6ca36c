<!-- 会员中心-store  和My bag-->
<template>
    <div class="app-container store-container">
        <div class="store-container-header">
            <div class="store-container-header-left" @click="goBack">
                <img src="@/assets/common/go-back.png" alt="">
            </div>
            <div class="store-container-header-right">
                <div class="store-container-header-right-tab-item" :class="{active: activeTab === 0}" @click="changeTab(0)">
                    Store
                </div>
                <div class="store-container-header-right-tab-item" :class="{active: activeTab === 1}" @click="changeTab(1)">
                    My bag
                </div>
            </div>
        </div>
        <!---下面部分-->
        <div class="store-container-content">
            <var-pull-refresh v-model="refreshing" @refresh="onRefresh">
                <swiper 
                    :slides-per-view="1" 
                    :space-between="0"
                    :allow-touch-move="true"
                    @swiper="onSwiper"
                    @slide-change="onSlideChange"
                    :initial-slide="activeTab"
                    class="store-swiper"
                >
                    <swiper-slide>
                        <div class="store-tab-content">
                            
                        </div>
                    </swiper-slide>
                    <swiper-slide>
                        <div class="mybag-tab-content">
                            <div class="mybag-tab-content-header">
                                <div class="mybag-tab-content-header-tab" v-for="item in mybagTabs" :key="item.id" :class="{active: item.id === myBagActiveTab}">
                                    {{ item.name }}
                                </div>
                            </div>
                            <!--下面内容部分-->
                            <div class="mybag-tab-content-box">
                                <div class="mybag-tab-content-box-list">

                                </div>
                                <div class="no-data" v-if="!myBagData.length">
                                    <img src="@/assets/common/no-mybag-data.png" alt="">
                                    <span>No data</span>
                                </div>
                            </div>
                        </div>
                    </swiper-slide>
                </swiper>
            </var-pull-refresh>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Swiper, SwiperSlide } from 'swiper/vue'
import 'swiper/css'

const router = useRouter()
const activeTab = ref(0)
const refreshing = ref(false)
const swiperInstance = ref<any>(null)
const myBagData = ref([])
const myBagActiveTab = ref(1)
const mybagTabs = ref([
    { id: 1, name: 'Enter Effect' },
    { id: 2, name: 'Avatar Frame' },
    { id: 3, name: 'Profile Card Frame' },
    { id: 4, name: ' Gift ' },
])

const changeTab = (index: number) => {
    activeTab.value = index
    if (swiperInstance.value) {
        swiperInstance.value.slideTo(index)
    }
}

const onSwiper = (swiper: any) => {
    swiperInstance.value = swiper
}

const onSlideChange = (swiper: any) => {
    activeTab.value = swiper.activeIndex
}

const onRefresh = async () => {
    // 模拟刷新数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    refreshing.value = false
}

const goBack = () => {
    router.back()
}

onMounted(() => {
    // 初始化时设置swiper到当前tab
    if (swiperInstance.value) {
        swiperInstance.value.slideTo(activeTab.value)
    }
})
</script>

<style lang='scss' scoped>
.store-container {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    font-family: var(--var-font-family-urbanist);
    height: 100vh;

    .store-container-header {
        height: 44px;
        padding: 0 15px;
        box-sizing: border-box;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        background: #000;
        z-index: 10;

        &-left {
            img {
                width: 24px;
                height: 24px;
            }
        }

        &-right {
            margin-left: 54px;
            flex: 1;
            display: flex;
            align-items: center;
            gap: 84px;
            font-size: 18px;
            font-weight: 700;
            line-height: 22px;

            &-tab-item {
                color: rgba($color: #fff, $alpha: .5);
                position: relative;
                cursor: pointer;
                transition: color 0.3s ease;
                
                &.active {
                    color: #fff;
                    &::before{
                        content:'';
                        position: absolute;
                        left: 0;
                        right: 0;
                        margin: 0 auto;
                        bottom: -4px;
                        width: 10px;
                        height: 3px;
                        border-radius: 2px;
                        background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                    }
                }
            }
        }
    }

    .store-container-content {
        margin-top: 6px;
        flex: 1;
        overflow: hidden;
        background: #000;
        padding: 0 15px;
        box-sizing: border-box;

        .store-swiper {
            height: 100%;
            
            .swiper-slide {
                height: 100%;
                overflow-y: auto;
                .store-tab-content,.mybag-tab-content{
                    height: 100%;
                }
                .mybag-tab-content{
                    .mybag-tab-content-header{
                        display: flex;
                        gap:5px;
                        overflow-x:auto;
                        .mybag-tab-content-header-tab{
                            padding: 6px 15px;
                            border-radius:18px;
                            font-size: 15px;
                            font-weight: 700;
                            color: rgba($color: #fff, $alpha: .5);
                            border: 1px solid transparent;
                            white-space: nowrap;
                            &.active{
                                color: $common-color;
                                border-color: $common-color;
                            }
                        }
                    }
                    .no-data{
                        margin-top:88px;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        img{
                            width: 160px;
                            height: 160px;
                        }
                        span{
                            margin-top: 15px;
                           font-size: 15px;
                           line-height: 18px;
                           font-weight: 700;
                           color: rgba($color: #fff, $alpha: .5);
                        }
                    }
                }
            }
        }
    }
}

// 自定义下拉刷新样式
:deep(.var-pull-refresh) {
    height: 100%;
}

:deep(.var-pull-refresh__track) {
    height: 100%;
}
</style>