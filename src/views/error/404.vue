<template>
    <div class="error-page">
        <div class="error-content">
            <div class="error-code">404</div>
            <div class="error-title">page not found</div>
            <div class="error-description">
                page not found
            </div>
            <div class="error-actions">
                <button class="btn-primary" @click="goHome">
                    back to home
                </button>
                <button class="btn-secondary" @click="goBack">
                    back to previous page
                </button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
    router.push('/home')
}

const goBack = () => {
    router.back()
}
</script>

<style lang="scss" scoped>
.error-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.error-content {
    text-align: center;
    padding: 2rem;
}

.error-code {
    font-size: 8rem;
    font-weight: bold;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.error-title {
    font-size: 2rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.error-description {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.error-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-primary,
.btn-secondary {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    backdrop-filter: blur(10px);

    &:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
    }
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);

    &:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.5);
    }
}

@media (max-width: 768px) {
    .error-code {
        font-size: 6rem;
    }

    .error-title {
        font-size: 1.5rem;
    }

    .error-actions {
        flex-direction: column;
        align-items: center;
    }
}
</style>