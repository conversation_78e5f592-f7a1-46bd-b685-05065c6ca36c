<!--  -->
<template>
    <div class="home-container">
        <!-- 顶部导航栏 -->
        <div class="header">
            <div class="tab-titles">
                <div v-for="(tab, index) in tabs" :key="tab.name" class="tab-title"
                    :class="{ active: activeTab === index }" @click="switchTab(index)">
                    {{ tab.title }}
                </div>
            </div>
            <div class="header-actions">
                <div class="search-icon"><img src="@/assets/common/notice.png" alt=""></div>
            </div>
        </div>

        <!-- Swiper 容器 -->
        <div class="swiper-container">
            <var-swipe ref="mySwipe" @change="onSwipeChange" :loop="false" class="mySwipe" :indicator="false">
                <!-- Following 标签页 -->
                <var-swipe-item>
                    <PullRefresh v-model="refreshLoading[0]" @refresh="onRefresh(0)" :threshold="60"
                        :success-duration="1500">
                        <var-list loading-text="正在努力输出" finished-text="一滴都没有了" error-text="出错了出错了"
                            :finished="tabs[0].finished" v-model:loading="tabs[0].loading" @load="load(0)">
                            <div class="slide-content" @scroll="handleScroll">
                                <div class="content-wrapper">
                                    <div class="content-wrapper-history">
                                        <div class="history-item" v-for="i in 3" :key="i">
                                            <div class="history-item-bg">
                                                <div class="history-item-bg-img">
                                                    <img src="@/assets/default.png" alt="" class="img-bg">
                                                </div>
                                                <div class="history-item-bg-is-live">
                                                    Live
                                                </div>
                                            </div>
                                            <span class="history-item-text">SAYRA💎</span>
                                        </div>
                                    </div>
                                    <MomentsCard :dataList="tabs[0].dataList" @sayHello="sayHello" />
                                </div>
                            </div>
                        </var-list>
                    </PullRefresh>
                </var-swipe-item>

                <!-- Hot 标签页 -->
                <var-swipe-item>
                    <PullRefresh v-model="refreshLoading[1]" @refresh="onRefresh(1)" :threshold="60"
                        :success-duration="1500">
                        <var-list loading-text="正在努力输出" finished-text="一滴都没有了" error-text="出错了出错了"
                            :finished="tabs[1].finished" v-model:loading="tabs[1].loading" @load="load(1)">
                            <div class="slide-content" @scroll="handleScroll">
                                <div class="content-wrapper">
                                    <MomentsCard :dataList="tabs[1].dataList" />
                                </div>
                            </div>
                        </var-list>
                    </PullRefresh>
                </var-swipe-item>

                <!-- New 标签页 -->
                <var-swipe-item>
                    <PullRefresh v-model="refreshLoading[2]" @refresh="onRefresh(2)" :threshold="60"
                        :success-duration="1500">
                        <var-list loading-text="正在努力输出" finished-text="一滴都没有了" error-text="出错了出错了"
                            :finished="tabs[2].finished" v-model:loading="tabs[2].loading" @load="load(2)">
                            <div class="slide-content" @scroll="handleScroll">
                                <div class="content-wrapper">
                                    <MomentsCard :dataList="tabs[2].dataList" />
                                </div>
                            </div>
                        </var-list>
                    </PullRefresh>
                </var-swipe-item>
            </var-swipe>
        </div>
        <sayHiPopup v-model:show="showSayHiPopup" @update:show="showSayHiPopup = $event" @confirmRecharge="confirmRecharge"/>
        <coinsNotEnoughPopup v-model:show="showCoinsNotEnoughPopup" @update:show="showCoinsNotEnoughPopup = $event" @showDiscount="showDiscountPopup = true"/>
        <discountPopup v-model:show="showDiscountPopup" @update:show="showDiscountPopup = $event"/>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, } from 'vue'
import { useRouter } from 'vue-router'
import PullRefresh from '@/components/PullRefresh.vue'
import MomentsCard from './components/momentsCard.vue'
import sayHiPopup from '@/components/sayHiPopup/sayHiPopup.vue'

import coinsNotEnoughPopup from '@/components/coinsNotEnough/coinsNotEnoughPopup.vue'
import discountPopup from '@/components/discountPopup/discountPopup.vue'

const showCoinsNotEnoughPopup = ref(false)
const showDiscountPopup = ref(false)
const mySwipe = ref<any>(null)
const router = useRouter()

// 标签页数据
const tabs = ref([
    { name: 'following', title: 'Following', page: 1, pageSize: 10, loading: false, finished: false, dataList: [] },
    { name: 'hot', title: 'Hot', page: 1, pageSize: 10, loading: false, finished: false, dataList: [] as any[] },
    { name: 'new', title: 'New', page: 1, pageSize: 10, loading: false, finished: false, dataList: [] },

])
const showSayHiPopup = ref(false)
const activeTab = ref(0)
const upMatchWrap = ref<HTMLElement | null>(null)


// 下拉刷新状态管理
const refreshLoading = ref([false, false, false, false])


const isScroll = ref(false)

const sayHello = () => {
    showSayHiPopup.value = true
}
const loadHotList = () => {
    if (!tabs.value[1].dataList.length) {
        tabs.value[1].dataList = [
            {
                id: 1,
                img: 'https://picsum.photos/300/200?random=20',
                name: 'Neblina🌹',
                title: 'Hot vibes only 🔥',
                age: 20,
                isOnline: true,
                isHot: true,
                isNew: false,
            },
            {
                id: 2,
                img: 'https://picsum.photos/300/200?random=21',
                name: 'Luna🌟',
                title: 'Night magic ✨',
                age: 20,
                isOnline: true,
                isHot: true,
                isNew: true,
            },
            {
                id: 3,
                img: 'https://picsum.photos/300/200?random=22',
                name: 'Stella💫',
                title: 'Star power ⭐',
                age: 20,
                isOnline: true,
                isHot: true,
                isNew: false,
            },
            {
                id: 4,
                img: 'https://picsum.photos/300/200?random=23',
                name: 'Aria🎵',
                title: 'Music vibes 🎶',
                age: 20,
                isOnline: true,
                isHot: true,
                isNew: true,
            },
            {
                id: 5,
                img: 'https://picsum.photos/300/200?random=24',
                name: 'Nova💎',
                title: 'Diamond dreams 💎',
                age: 20,
                isOnline: true,
                isHot: true,
                isNew: false,
            },
            {
                id: 6,
                img: 'https://picsum.photos/300/200?random=25',
                name: 'Iris🌸',
                title: 'Flower power 🌸',
                age: 20,
                isOnline: true,
                isHot: true,
                isNew: true,
            },
            {
                id: 7,
                img: 'https://picsum.photos/300/200?random=26',
                name: 'Zara🔥',
                title: 'Fire dance 🔥',
                age: 20,
                isOnline: true,
                isHot: true,
                isNew: false,
            },
            {
                id: 8,
                img: 'https://picsum.photos/300/200?random=27',
                name: 'Maya🌙',
                title: 'Moonlight magic 🌙',
                age: 20,
                isOnline: true,
                isHot: true,
                isNew: true,
            },
            {
                id: 9,
                img: 'https://picsum.photos/300/200?random=28',
                name: 'Kira✨',
                title: 'Sparkle time ✨',
                age: 20,
                isOnline: true,
                isHot: true,
                isNew: false,
            },
            {
                id: 10,
                img: 'https://picsum.photos/300/200?random=29',
                name: 'Sage🌿',
                title: 'Nature vibes 🌿',
                age: 20,
                isOnline: true,
                isHot: true,
                isNew: true,
            },
        ]
    } else {
        // 如果已有数据，可以添加更多数据或更新现有数据
        console.log('Hot list already loaded')
    }
}

const loadNewList = () => {
    if (!tabs.value[2].dataList.length) {
        tabs.value[2].dataList = [
            {
                id: 1,
                img: 'https://picsum.photos/300/200?random=20',
                name: 'Neblina🌹',
                title: 'Hot vibes only 🔥',
                age: 20,
                isOnline: true,
                isHot: true,
                isNew: false,
            },
            {
                id: 1,
                img: 'https://picsum.photos/300/200?random=20',
                name: 'Neblina🌹',
                title: 'Hot vibes only 🔥',
                age: 20,
                isOnline: true,
                isHot: true,
                isNew: false,
            }
        ]
    } else {
        // 如果已有数据，可以添加更多数据或更新现有数据
        console.log('Hot list already loaded')
    }
}


// Swipe 事件处理
const onSwipeChange = (index: number) => {
    activeTab.value = index
    if (index === 1 && tabs.value[1].dataList.length === 0) {
        loadHotList()
    }
}

// 切换标签页
const switchTab = (index: number) => {
    activeTab.value = index
    // 如果需要通过 ref 控制 swipe，可以取消注释下面这行
    mySwipe.value?.to(index)
    if (index === 1 && tabs.value[1].dataList.length === 0) {
        loadHotList()
    }
}



// 下拉刷新处理
const onRefresh = async (index: number) => {
    refreshLoading.value[index] = true
    console.log(`刷新 ${tabs.value[index].title} 页面`)

    // 模拟异步请求
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 这里可以添加具体的刷新逻辑
    // 例如：重新获取数据、更新内容等

    // 刷新完成后，显示成功状态
    refreshLoading.value[index] = false

    console.log(`${tabs.value[index].title} 页面刷新完成`)
}

// 上拉加载处理
const load = async (index: number) => {
    tabs.value[index].loading = true
    if (index === 0) {
        await new Promise(resolve => setTimeout(() => {
            tabs.value[index].dataList = [...tabs.value[index].dataList, ...tabs.value[index].dataList]
            resolve(true)
            if (tabs.value[index].dataList.length > 30) {
                tabs.value[index].finished = true
            }
        }, 1000))
    } else if (index === 1) {
        await new Promise(resolve => setTimeout(() => {
            // 为 Hot 标签页添加更多数据
            const newHotData = [
                {
                    id: 1,
                    img: 'https://picsum.photos/300/200?random=20',
                    name: 'Neblina🌹',
                    title: 'Hot vibes only 🔥',
                    age: 20,
                    isOnline: true,
                    isHot: true,
                   
                },
                {
                    id: 1,
                    img: 'https://picsum.photos/300/200?random=20',
                    name: 'Neblina🌹',
                    title: 'Hot vibes only 🔥',
                    age: 20,
                    isOnline: true,
                    isHot: true,
                }
            ]
            tabs.value[index].dataList = [...tabs.value[index].dataList, ...newHotData]
            resolve(true)
            if (tabs.value[index].dataList.length > 20) {
                tabs.value[index].finished = true
            }
        }, 1000))
    }
    if (index === 2 && tabs.value[2].dataList.length === 0) {
        loadNewList()
    }
    tabs.value[index].loading = false
}
const handleScroll = (event: any) => {
    isScroll.value = true
    if (event.target.scrollTop > 100) {
        upMatchWrap.value?.classList.add('is-show')
    } else {
        upMatchWrap.value?.classList.remove('is-show')
    }
    setTimeout(() => {
        isScroll.value = false
    }, 1000)
}
const handleUpMatch = () => {
    const scrollDom = document.getElementsByClassName('slide-content')[activeTab.value]
    if (scrollDom) {
        scrollDom.scrollTo({
            top: 0,
            behavior: 'smooth'
        })
        upMatchWrap.value?.classList.remove('is-show')
    }
}

const confirmRecharge = () => {
    showCoinsNotEnoughPopup.value = true
}

onMounted(() => {
    setTimeout(() => {
        tabs.value[0].dataList = [
            {
                id: 1,
                avatar: 'https://picsum.photos/300/200?random=2',
                name: 'SAYRA💎',
                title: 'Are you a good driver on curves?!🔥☁️',
                isFollow: true,
                age: 32,
                isOnline: true,
                isHot: true,
                moments: [
                    {
                        id: 1,
                        img: 'https://ilivegirl.s3.amazonaws.com/main/79ebc489b875357c6ae8dfee20b7836120250727164040-medium.jpg',
                        content: 'Bad girls club ✨',
                    },
                    {
                        id: 2,
                        img: 'https://ilivegirl.s3.amazonaws.com/main/79ebc489b875357c6ae8dfee20b7836120250727164040-medium.jpg',
                        content: 'Bad girls club ✨',
                    },

                ]
            },
            {
                id: 2,
                avatar: 'https://picsum.photos/300/200?random=3',
                name: 'SAYRA💎',
                title: 'Are you a good driver on curves?!🔥☁️',
                isFollow: false,
                age: 32,
                isOnline: true,
                moments: [
                    {
                        id: 1,
                        img: 'https://ilivegirl.s3.amazonaws.com/main/79ebc489b875357c6ae8dfee20b7836120250727164040-medium.jpg',
                        content: 'Bad girls club ✨',
                    },
                ]
            },
        ]
    }, 1000)
})
</script>

<style lang="scss" scoped>
.home-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #1a1a1a;
    color: #fff;
}

.header {
    background: rgba(26, 26, 26, 0.95);
    padding: 15px 15px;
    position: sticky;
    top: 0;
    z-index: 100;
    display: flex;
    justify-content: space-between;
    align-items: center;
    backdrop-filter: blur(10px);
}

.tab-titles {
    display: flex;
    gap: 15px;

    .tab-title {
        padding: 8px 0;
        font-size: 18px;
        font-weight: 600;
        line-height: 21px;
        color: rgba(255, 255, 255, 0.7);
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;

        &.active {
            color: #fff;
            font-weight: 600;

            &::after {
                content: '';
                position: absolute;
                bottom: -5px;
                left: 0;
                right: 0;
                margin: 0 auto;
                width: 10px;
                height: 3px;
                background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                border-radius: 2px;
            }
        }
    }
}



.header-actions {
    display: flex;
    gap: 20px;

    img {
        width: 24px;
        height: 24px;
    }
}

.swiper-container {
    flex: 1;
    overflow: hidden;
}

.mySwipe {
    height: 100%;
}





.slide-content {
    height: 100%;
    // padding-bottom: 88px;
    overflow-y: auto;
    background: #1a1a1a;
}

.content-wrapper {

    padding: 0px 15px;
    // max-width: 750px;
    margin: 0 auto;
    margin-top: 10px;

    .content-wrapper-history {
        display: flex;
        gap: 10px;
        overflow-x: auto;
        margin-bottom: 20px;

        .history-item {
            flex-shrink: 0;
            width: 54px;
            height: auto;

            .history-item-bg {
                width: 100%;
                height: 54px;
                border-radius: 50%;
                background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                border-radius: 50%;
                padding: 1.5px;
                position: relative;
                display: flex;
                justify-content: center;
                align-items: center;

                .history-item-bg-img {
                    width: 100%;
                    padding: 1.5px;
                    background-color: #1a1a1a;
                    border-radius: 50%;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    .img-bg {
                        width: 100%;
                        height: 100%;
                        border-radius: 50%;
                    }
                }

                .history-item-bg-is-live {
                    position: absolute;
                    bottom: 0;
                    padding: 1px 7px;
                    background: $common-color;
                    border-radius: 4px;
                    font-size: 10px;
                    font-weight: 600;
                    color: #fff;
                    line-height: 12px;
                    text-align: center;
                }
            }

            .history-item-text {
                font-size: 12px;
                font-weight: 600;
                color: rgba(255, 255, 255, 0.7);
                line-height: 14px;
                text-align: center;
                margin-top: 7px;
                white-space: nowrap;
            }
        }
    }

    .hot-content-search {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 8px;

        .hot-content-search-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            padding: 10px 0;
            background: #1E1D38;
            border-radius: 8px;
            transition: all 0.3s ease;

            .hot-content-search-item-img {
                width: 24px;
                height: 24px;

                &.first {
                    width: 20px;
                    height: 20px;
                }
            }

            .hot-content-search-item-text {
                font-size: 15px;
                font-weight: 700;
                line-height: 18px;
                color: #fff;
            }

            &.active {
                background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
            }
        }
    }
}

.hunting-match-wrap {
    position: fixed;
    width: 100%;
    height: 109px;
    bottom: calc(48px + env(safe-area-inset-bottom));
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    z-index: 5000;

    .quick-match-btn-wrap {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 5px;
        position: relative;
        transition: all .8s cubic-bezier(0.4, 0, 0.2, 1);
        width: max-content;
        height: max-content;
        background: linear-gradient(96deg, #ffc83c 0%, #ff45cb 41%, #ca55e2 100%);
        border-radius: 32px;
        transform: translateY(0);
        opacity: 1;
        will-change: transform, opacity;

        &.is-scroll {
            transform: translateY(200%);
            opacity: 0;
            pointer-events: none;
        }

        .quick-match-btn {
            min-width: 160px;
            border-radius: 32px;
            border: 1px solid rgba(255, 255, 255, .42);
            box-sizing: border-box;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 22px 8px 9px;

            &:hover {
                box-shadow: 0 12px 32px rgba(255, 107, 157, 0.4);
            }

            .match-avatar {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                overflow: visible;
                // border: 2px solid #fff;
                position: relative;

                .avatar-swiper {
                    // width: 100%;
                    // height: 100%;
                    border-radius: 50%;
                    overflow: visible;
                    perspective: 800px;
                    margin-top: 10px;

                    :deep(.swiper-wrapper) {
                        border-radius: 50%;
                        transform-style: preserve-3d;
                    }

                    :deep(.swiper-slide) {
                        border-radius: 50%;
                        overflow: hidden;
                        transform-style: preserve-3d;
                        backface-visibility: hidden;
                        width: 27px !important;
                        height: 27px !important;
                        // border: 2px solid #fff;
                        // box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                        transition: all 0.3s ease;
                    }

                    :deep(.swiper-slide-active) {
                        // box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
                        transform: scale(1.05);
                    }
                }

                .avatar-slide {
                    width: 100%;
                    height: 100%;
                    border-radius: 50%;
                    overflow: hidden;

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: contain;
                        border-radius: 50%;
                        border: 1px solid #fff;
                    }
                }
            }

            .match-text {
                margin-left: 7px;

                .match-title {
                    font-size: 15px;
                    font-weight: 600;
                    color: #fff;
                    line-height: 18px;
                    // background: linear-gradient(153deg, #ffffff, #f9f2bb);
                    // -webkit-background-clip: text;
                    // color: transparent;
                    // box-sizing: border-box;
                    // transform: translate(-3px);
                }

                .match-subtitle {
                    font-size: 12px;
                    color: #fff;
                    line-height: 14px;

                    .online-hosts {
                        color: #FFF316;
                    }
                }
            }
        }
    }


    .match-instruction {
        width: 100%;
        position: absolute;
        left: 0;
        bottom: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #1B1B34;
        padding: 8px 6px 8px 15px;
        box-sizing: border-box;

        span {
            font-size: 12px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.7);
        }

        .close-icon {
            width: 18px;
            height: 18px;
        }
    }
}

.up-match-wrap {
    position: fixed;
    width: 68px;
    height: 68px;
    bottom: calc(90px + env(safe-area-inset-bottom));
    right: 20px;
    transform: scale(0);
    transition: all .3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 5001;

    &.is-show {
        transform: scale(1);
    }

    img {
        width: 100%;
        height: 100%;
    }
}

:deep(.var-list) {
    height: 100%
}
</style>