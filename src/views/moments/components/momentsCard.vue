<!--  -->
<template>
    <div class="moments-card">
        <div class="moments-card-item" v-for="item in dataList" :key="item.id">
            <div class="moments-card-item-header">
                <div class="moments-card-item-header-left">
                    <div class="left-left-box">
                        <img :src="item.avatar" alt="" class="moments-card-item-header-left-avatar">
                        <div class="is-online" v-if="item.isOnline">
                        </div>
                    </div>
                    <div class="left-right-box">
                        <span class="name">{{ item.name }}</span>
                        <div class="bradge-box">
                            <div class="age-box">
                                <img src="@/assets/home/<USER>" alt="" class="gril-icon">
                                <span class="age">{{ item.age }}</span>
                            </div>
                            <img src="@/assets/home/<USER>" alt="" class="hot-icon" v-if="item.isHot">
                        </div>
                    </div>
                </div>
                <div class="moments-card-item-header-right">
                    <div class="follow-btn" v-if="item.isFollow">
                        <span class="follow-btn-text">+ Follow</span>
                    </div>
                    <img src="@/assets/common/more-infor.png" alt="" class="more-infor-icon">
                </div>
            </div>
            <div class="moments-card-item-content">
                <div class="moments-card-item-content-title">
                    <span class="title-text">{{ item.title }}</span>
                </div>
                <div class="moments-card-item-content-img">
                    <div class="moments-card-item-content-img-one" v-if="item.moments&&item.moments.length === 1">
                        <img :src="item.moments[0].img" alt="">
                    </div>
                    <div class="moments-card-item-content-img-repeat" v-else-if="item.moments&&item.moments.length > 1">
                        <div class="moments-card-item-content-img-repeat-item" v-for="i in item.moments" :key="i.id">
                            <img :src="i.img" alt="">
                        </div>
                    </div>
                </div>
            </div>
            <!---下面的点赞和评论以及日期-->
            <div class="moments-card-item-footer">
                <div class="moments-card-item-footer-left">
                   19/07
                </div>
                <div class="moments-card-item-footer-right">
                    <div class="moments-card-item-footer-right-box">
                        <img src="@/assets/common/like.png" alt="" class="like-icon">
                        <span class="like-text">19</span>
                    </div>
                    <div class="moments-card-item-footer-right-box">
                        <img src="@/assets/common/message-count.png" alt="" class="like-icon">
                        <span class="like-text">19</span>
                    </div>
                    <div class="moments-card-item-footer-right-box" @click="sayHello">
                        <img src="@/assets/common/say-hi.png" alt="" class="like-icon">
                        <span class="like-text say-hi-text" >Say Hi ~</span>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup name="momentsCard">
import { ref } from 'vue'
const { dataList } = defineProps<{
    dataList: any[]
}>()

const emit = defineEmits(['sayHello'])

const sayHello = () => {
    emit('sayHello')
}
</script>
<style lang='scss' scoped>
.moments-card {
    width: 100%;
    height: 100%;

    .moments-card-item {
        margin-bottom: 30px;

        .moments-card-item-header {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .moments-card-item-header-left {
                display: flex;
                align-items: center;
                gap: 8px;

                .left-left-box {
                    display: flex;
                    align-items: center;
                    position: relative;

                    .moments-card-item-header-left-avatar {
                        width: 36px;
                        height: 36px;
                        border-radius: 50%;
                        object-fit: cover;
                    }

                    .is-online {
                        position: absolute;
                        right: 2px;
                        bottom: 2px;
                        width: 6px;
                        height: 6px;
                        border-radius: 50%;
                        background-color: $success-color;
                    }
                }

                .left-right-box {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;

                    .name {
                        font-size: 16px;
                        font-weight: 700;
                        color: #fff;
                        line-height: 19px;
                    }

                    .bradge-box {
                        margin-top: 3px;
                        display: flex;
                        align-items: center;
                        gap: 4px;


                        .age-box {
                            display: flex;
                            align-items: center;
                            gap: 1px;
                            width: max-content;
                            background-color: $common-color;
                            padding: 1px 4px;
                            border-radius: 6px;

                            .gril-icon {
                                width: 10px;
                                height: 10px;
                            }

                            .age {
                                font-size: 10px;
                                font-weight: 800;
                                color: #fff;
                                line-height: 12px;
                            }
                        }

                        .hot-icon {
                            height: 12px;
                        }
                    }
                }
            }

            .moments-card-item-header-right {
                display: flex;
                align-items: center;
                gap: 10px;

                .follow-btn {
                    color: #fff;
                    padding: 5.5px 9px;
                    background: linear-gradient(90deg, #FF812B 0%, #FF0BC2 100%);
                    border-radius: 14px;
                    font-weight: 700;
                    line-height: 17px;
                }

                .more-infor-icon {
                    width: 24px;
                    height: 24px;
                }
            }
        }

        .moments-card-item-content {
            margin-top: 10px;

            .moments-card-item-content-title {
                .title-text {
                    font-size: 15px;
                    font-weight: 700;
                    color: #fff;
                    line-height: 18px;
                }
            }

            .moments-card-item-content-img {
                margin-top: 12px;

                .moments-card-item-content-img-one {
                    img {
                        width: 180px;
                        max-height: 255px;
                        min-height: 113px;
                        object-fit: cover;
                    }
                }
                .moments-card-item-content-img-repeat{
                    display: grid;
                    grid-template-columns: repeat(3, 1fr);
                    gap: 10px;
                    .moments-card-item-content-img-repeat-item{
                        img{
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                        }
                    }
                }
            }
        }
        .moments-card-item-footer{
            margin-top: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .moments-card-item-footer-left{
                font-size: 12px;
                font-weight: 600;
                color: rgba($color: #fff, $alpha: 0.5);
                line-height: 14px;
            }
            .moments-card-item-footer-right{
                display: flex;
                align-items: center;
                gap: 20px;
                .moments-card-item-footer-right-box{
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    .like-icon{
                        width: 18px;
                        height: 18px;
                    }
                    .like-text{
                        font-size: 12px;
                        font-weight: 600;
                        color: rgba($color: #fff, $alpha: 0.5);
                        line-height: 14px;
                    }
                    .say-hi-text{
                        color:$common-color;
                    }
                }
            }
        }
    }
}
</style>