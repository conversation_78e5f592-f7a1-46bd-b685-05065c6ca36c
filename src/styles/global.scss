::-webkit-scrollbar {
	width: 0;
	height: 0;
}

// Hide scrollbar for IE, Edge and Firefox
* {
	-ms-overflow-style: none; /* IE and Edge */
	scrollbar-width: none !important; /* Firefox */
	-webkit-overflow-scrolling: touch;
}

html {
	touch-action: manipulation;
	margin: 0 auto;
	line-height: 1.2;
	color: #333;
	// max-width: 750px;
}

body {
	width: 100%;
	height: 100%;
	overflow-x: hidden;
}
img {
	user-select: none;
	-webkit-user-drag: none;
}
#app {
	width: 100%;
	height: 100%;
	background: #000;
	scroll-behavior: smooth;
	.app-container {
		width: 100%;
		height: 100%;
		padding-top: env(safe-area-inset-top);
		display: flex;
		flex-direction: column;
        .main-container{
            flex: 1;
        }
		// padding-bottom: 50px;
	}
}
