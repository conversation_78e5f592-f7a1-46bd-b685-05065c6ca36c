:root {
	--popup-overlay-background-color: rgba(0, 0, 0, 0.46) !important;
	--font-family-type1: 'TTNormsPro-Bold,TTNormsPro';
	--font-family-type3: 'TTNormsPro-Medium,TTNormsPro';
	--font-family-type2: 'TTNormsPro-Regular,TTNormsPro';
	--font-family-type4: 'TTNormsPro-BoldItalic,TTNormsPro';
	--font-family-type5: 'TTNormsPro-ExtraBoldItalic,TTNormsPro';
	--font-family-urbanist: 'Urbanist-Variable';
}

/* 增加更高优先级的选择器 */
html :root {
	--popup-overlay-background-color: rgba(0, 0, 0, 0.46) !important;
}

body :root {
	--popup-overlay-background-color: rgba(0, 0, 0, 0.46) !important;
}

#app :root {
	--popup-overlay-background-color: rgba(0, 0, 0, 0.46) !important;
}
.var-snackbar__wrapper{
    width:calc(100% - 30px)!important;
    padding:18px 20px;
    box-sizing: border-box;
    .var-snackbar__content{
        padding:0;
    }
}
/*  */
