/* 渐变移动动画 */
@keyframes gradientMove {
	0% {
		background-position: 0% 50%;
	}
	50% {
		background-position: 100% 50%;
	}
	100% {
		background-position: 0% 50%;
	}
}

/* 光晕扫过动画 */
@keyframes shimmer {
	0% {
		left: -100%;
	}
	100% {
		left: 100%;
	}
}

/* 脉冲动画 */
@keyframes pulse {
	0% {
		width: 0;
		height: 0;
		opacity: 1;
	}
	100% {
		width: 200px;
		height: 200px;
		opacity: 0;
	}
}
@keyframes opac-animation {
	0% {
		opacity: 0;
		width: 0px;
		height: 0px;
		border-radius: 14px;
		margin: 16px;
		transform: scale(1);
	}

	10% {
		opacity: 0;
		width: 184px;
		height: 50px;
		border-radius: 14px;
		margin: 16px;
	}

	15% {
		opacity: 0.75;
		width: 184px;
		height: 50px;
		border-radius: 14px;
		margin: 16px;
	}

	35% {
		opacity: 0;
		width: 216px;
		height: 82px;
		margin: 0;
		border-radius: 30px;
	}

	55% {
		opacity: 0;
		width: 0px;
		height: 0px;
		margin: 16px;
		top: 0;
		left: 0;
	}

	to {
		opacity: 0;
		width: 0;
		height: 0;
		top: 0;
		left: 0;
	}
}
@keyframes call-animation {
	0% {
		transform: scale(1);
	}
	10% {
		transform: scale(0.94);
	}
	15% {
		transform: scale(1);
	}
	100% {
		transform: scale(1);
	}
}
@keyframes call-swing {
	0%,
	65% {
		transform: rotate(0);
	}
	70% {
		transform: rotate(8deg);
	}
	75% {
		transform: rotate(-8deg) scale(1.15);
	}
	80% {
		transform: rotate(8deg) scale(1.15);
	}
	85% {
		transform: rotate(-8deg);
	}
	90% {
		transform: rotate(8deg);
	}
	95% {
		transform: rotate(-8deg);
	}
	100% {
		transform: rotate(0);
	}
}
@keyframes innerWave {
	0% {
		opacity: 0;
		transform: scale(0.1, 0, 1);
	}
	10% {
		opacity: 0.8;
		transform: scale(0.6);
	}
	30% {
		opacity: 0.6;
		transform: scale(1);
	}
	50% {
		opacity: 0.5;
		transform: scale(1.3);
	}
	85% {
		opacity: 0.2;
		transform: scale(1.72);
	}
	100% {
		opacity: 0;
		transform: scale(1.72);
	}
}
@keyframes up-down {
	0% {
		transform: translateY(0px);
	}
	10% {
		transform: translateY(-5px);
	}
	80% {
		transform: translateY(-20px);
		opacity: 1;
	}
	100% {
		transform: translateY(0px);
	}
}
/* 旋转渐变动画 (可选) 
// @keyframes conicRotate {
//     from {
//         transform: rotate(0deg);
//     }
//     to {
//         transform: rotate(360deg);
//     }
// }

// // CSS 自定义属性渐变旋转 (可选)
// @keyframes gradientRotate {
//     to {
//         --gradient-angle: 360deg;
//     }
// }
*/
