const memberShipModule = [
    {
        path: '/memberShip/wallet',
        name: 'Wallet',
        component: () => import('@/views/memberShip/wallet.vue'),
        meta: {
            title: 'Wallet'
        }
    },
    {
        path: '/memberShip/getSvip',
        name: 'GetSvip',
        component: () => import('@/views/memberShip/getSvip.vue'),
        meta: {
            title: 'GetSvip'
        }
    },
    {
        path: '/memberShip/store',
        name: 'Store',
        component: () => import('@/views/memberShip/store.vue'),
        meta: {
            title: 'Store'
        }
    },
]

export default memberShipModule