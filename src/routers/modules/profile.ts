
const profileModule = [
    {
        path: '/profile/edit',
        name: 'EditProfile',
        component: () => import('@/views/profile/editProfile.vue'),
        meta: {
            title: 'EditProfile'
        }
    },
    {
        path: '/profile/setting',
        name: 'Setting',
        component: () => import('@/views/profile/setting.vue'),
        meta: {
            title: 'Setting'
        }
    },
    {
        path: '/profile/account',
        name: 'Account',
        component: () => import('@/views/profile/myAccount.vue'),
        meta: {
            title: 'Account'
        }
    },
    {
        path: '/profile/bindEmail',
        name: 'BindEmail',
        component: () => import('@/views/profile/bindEmail.vue'),
        meta: {
            title: 'BindEmail'
        }
    },
    {
        path: '/profile/changePassword',
        name: 'ChangePassword',
        component: () => import('@/views/profile/changePassword.vue'),
        meta: {
            title: 'ChangePassword'
        }
    },
    {
        path: '/profile/forgetPassword',
        name: 'ForgetPassword',
        component: () => import('@/views/profile/forgetPassword.vue'),
        meta: {
            title: 'ForgetPassword'
        }
    },
    {
        path: '/profile/languageSwitch',
        name: 'LanguageSwitch',
        component: () => import('@/views/profile/languageSwitch.vue'),
        meta: {
            title: 'LanguageSwitch'
        }
    },
    {
        path: '/profile/myLevel',
        name: 'MyLevel',
        component: () => import('@/views/profile/myLevel.vue'),
        meta: {
            title: 'myLevel'
        }
    },
    {
        path: '/profile/consumptionRecord',
        name: 'ConsumptionRecord',
        component: () => import('@/views/profile/consumptionRecord.vue'),
        meta: {
            title: 'consumptionRecord'
        }
    },
    {
        path: '/profile/blackList',
        name: 'BlackList',
        component: () => import('@/views/profile/blackList.vue'),
        meta: {
            title: 'blackList'
        }
    }
]

export default profileModule