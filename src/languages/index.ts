import { createI18n } from 'vue-i18n'
// 获取浏览器默认语言
const getBrowserLanguage = () => {
    const browserLang = navigator.language || (navigator as any).userLanguage
    // 获取语言代码的前两个字符（例如：'zh-CN' -> 'zh'）
    const langCode = browserLang.split('-')[0]
    return langCode
}

// 获取支持的语言列表
const supportedLanguages = ['en', 'zh', 'es', 'de', 'fr', 'pt','hi','nl','fi','da','el','ja','ko','id','ms','th','it','zh-TW','vi','sv','no','cs','hu','tr','ru','bn','ar','pl','sk','uk']
// 需要RTL方向支持的语言
const rtlLanguages = ['ar'];
// 设置HTML页面的dir属性
const setDirection = (lang: string) => {
    const dir = rtlLanguages.includes(lang) ? 'rtl' : 'ltr';
    document.documentElement.setAttribute('dir', dir);
};
// 保存语言设置到 localStorage
const saveLanguage = (lang: string) => {
    if (supportedLanguages.includes(lang)) {
        localStorage.setItem('language', lang)
    }
}

// 获取初始语言
const getInitialLocale = () => {
    // 1. 优先使用本地存储的语言设置
    const storedLang = localStorage.getItem('language')
    if (storedLang && supportedLanguages.includes(storedLang)) {
        return storedLang
    }

    // 2. 如果没有本地存储，使用浏览器默认语言
    const browserLang = getBrowserLanguage()
    if (supportedLanguages.includes(browserLang)) {
        // 保存浏览器默认语言到 localStorage
        saveLanguage(browserLang)
        return browserLang
    }

    // 3. 如果浏览器语言不支持，默认使用英语
    saveLanguage('en')
    return 'en'
}
const initialLocale = getInitialLocale();
setDirection(initialLocale);
const i18n = createI18n({
    legacy: false,
    locale: initialLocale,
    messages: {
    
    }
})

// 监听语言变化
i18n.global.locale.value = getInitialLocale()

// 导出保存语言的函数，供其他组件使用
export const setLanguage = (lang: string) => {
    if (supportedLanguages.includes(lang)) {
        i18n.global.locale.value = lang;
        saveLanguage(lang);
        setDirection(lang);
    }
}

export default i18n