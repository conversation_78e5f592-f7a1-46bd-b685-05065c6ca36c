import { defineStore } from 'pinia'
import i18n from '@/languages'

export const useLanguageStore = defineStore('language', {
    state: () => ({
        language: localStorage.getItem('language') || 'en'
    }),
    getters: {
        getLanguage: (state) => state.language
    },
    actions: {
        setLanguage(lang: string) {
            this.language = lang
            localStorage.setItem('language', lang)
            i18n.global.locale.value = lang
            // 设置页面的文字方向
            const rtlLangs = ['ar'] // 可按需添加更多
            const dir = rtlLangs.includes(lang) ? 'rtl' : 'ltr'
            document.documentElement.setAttribute('dir', dir)
        }
    }
})