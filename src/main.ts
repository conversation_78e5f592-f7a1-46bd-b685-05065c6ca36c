import { createApp } from 'vue'
import App from './App.vue'
import { createPinia } from 'pinia'
import router from './routers/index'
import './utils/rem'
import './styles/main.scss'
import Varlet from '@varlet/ui'
import '@varlet/ui/es/style'

import {Switch,PasswordInput} from 'vant'
import 'vant/es/switch/style'
import 'vant/es/password-input/style'


const pinia = createPinia()
const app = createApp(App)

app.use(router)
app.use(pinia)
app.use(Varlet)
app.use(Switch)
app.use(PasswordInput)
router.isReady().then(() => {
    app.mount('#app')
})
