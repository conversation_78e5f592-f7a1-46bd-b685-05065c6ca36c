<template>
    <Suspense>
        <template #default>
            <router-view v-slot="{ Component, route }">
                <transition :name="transitionName" mode="out-in">
                    <keep-alive :include="include" :max="10">
                        <component :is="Component" v-if="route.meta.keepAlive" :key="getFirstLevelRoute(route).name" />
                    </keep-alive>
                </transition>
                <transition :name="transitionName" mode="out-in">
                    <component :is="Component" v-if="!route.meta.keepAlive && isRouterAlive"
                        :key="getFirstLevelRoute(route).name"></component>
                </transition>
            </router-view>
        </template>
        <template #fallback>
            <div>Loading...</div>
        </template>
    </Suspense>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'
const route = useRoute()
const include = ref([])
const transitionName = ref('slide-left')
const isRouterAlive = ref(true)
const getFirstLevelRoute = function (route: { matched: string | any[] }) {
    if (!Array.isArray(route.matched) || route.matched.length === 0) {
        return route
    }
    return route.matched[0]
}
</script>

<style lang="scss" scoped>
.slide-left-enter-from {
    transform: translateX(20px);
    opacity: 0.5;
}

.slide-left-enter-to {
    transform: translateX(0px);
}

.slide-left-leave-from {
    transform: translateX(0);
}

.slide-left-leave-to {
    transform: translateX(20px);
    opacity: 0.5;
}

.slide-left-enter-active,
.slide-left-leave-active {
    transition: all 0.3s;
}
</style>
