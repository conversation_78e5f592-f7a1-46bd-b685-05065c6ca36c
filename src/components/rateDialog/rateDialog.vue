<!-- 选择性别弹窗 -->
<template>
    <div>
        <var-popup position="center" v-model:show="showRateDialog" @click-overlay="close" :safe-area="true"
            :default-style="false">
            <div class="rate-us-dialog">
                <div class="rate-us-dialog-box">
                    <div class="rate-us-dialog-box-title">
                        <span>Rate Us</span>
                    </div>
                    <!--描述-->
                    <div class="rate-us-dialog-box-desc">
                        <span>Your opinion matters to us</span>
                        <span>^o^</span>
                    </div>
                </div>
                <div class="rate-us-dialog-close">
                    <img src="@/assets/common/close-rate-icon.png" alt="" class="close-icon" @click="close">
                </div>

            </div>
        </var-popup>
    </div>
</template>

<script lang="ts">
export default {
    name: 'rateDialog',
    props: {
        show: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            selectGender: 'male',
            showRateDialog: this.show
        };
    },
    watch: {
        show: {
            handler(newVal) {
                this.showRateDialog = newVal
            },
            immediate: true
        }
    },
    mounted() {

    },
    methods: {
        close() {
            this.showRateDialog = false
            this.$emit('close')
            this.$emit('update:show', false)
        },
    }
}
</script>
<style lang='scss' scoped>
.rate-us-dialog {
    font-family: var(--font-family-urbanist);

    .rate-us-dialog-box {
        width: 280px;
        height: 248px;
        background: url('@/assets/common/rate-us.png') no-repeat center center;
        background-size: 100% 100%;
        border-radius: 16px;
        padding: 21px 0 0;

        .rate-us-dialog-box-title {
            font-size: 24px;
            font-weight: 700;
            line-height: 29px;
        
            font-style: italic;
            background: linear-gradient(180deg, #FFFFFF 18.97%, #FFECAD 113.79%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-align: center;
        }
        .rate-us-dialog-box-desc{
            margin-top: 60px;
            text-align: center;
            font-size: 16px;
            font-weight: 700;
            line-height: 19px;
            color: rgba($color: #fff, $alpha: .5);
            display: flex;
            flex-direction: column;

        }
    }

    .rate-us-dialog-close {
        margin-top: 30px;
        display: flex;
        justify-content: center;

        img {
            width: 30px;
            height: 30px;
        }
    }
}
</style>