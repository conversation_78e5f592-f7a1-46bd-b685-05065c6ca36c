<!-- 选择性别弹窗 -->
<template>
    <div class="first-match">
        <var-popup position="bottom" v-model:show="firstMatchDialogShow"  :safe-area="true"
            :default-style="false" :close-on-click-overlay="false">
            <div class="first-match-dialog">
                <img src="@/assets/home/<USER>" alt="" class="close-dialog" @click="close">
                <div class="first-match-dialog-content">
                    <div class="first-match-dialog-content-title">
                        Free Call with Her
                    </div>
                    <div class="first-match-dialog-content-desc">
                        Congrats! You've earned a free call Start
                        matching now~
                    </div>
                    <div class="free-modal-content">
                        <div class="free-modal-content-inner">
                            <!---中间扩散的涟漪-->
                            <div class="free-modal-content-c">
                                <div class="free-modal-content-w free-modal-content-w1"></div>
                                <div class="free-modal-content-w free-modal-content-w2"></div>
                                <div class="free-modal-content-w free-modal-content-w3"></div>
                                <div class="free-modal-content-call">
                                    <div class="free-modal-content-call-icon"></div>
                                    <div class="free-modal-content-call-text">Free</div>
                                </div>
                            </div>
                            <!---主播们的头像-->
                            <div class="free-modal-content-anchor">
                                <div class="free-modal-content-anchor-item" v-for="(item, index) in 6" :key="index"
                                    @animationiteration="onAnimationIteration(index)">
                                    <img class="free-modal-content-anchor-item-img" src="@/assets/home/<USER>" alt=""
                                        :style="{ display: getCurrentImage(index) === 0 ? 'block' : 'none' }">
                                    <img class="free-modal-content-anchor-item-img" src="@/assets/home/<USER>" alt=""
                                        :style="{ display: getCurrentImage(index) === 1 ? 'block' : 'none' }">
                                    <img class="free-modal-content-anchor-item-img" src="@/assets/home/<USER>" alt=""
                                        :style="{ display: getCurrentImage(index) === 2 ? 'block' : 'none' }">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="free-hunting-footer">
                        <div class="free-hunting-btn">
                            <div class="free-hunting-btn-inner">Match now</div>
                            <div class="free-hunting-btn-ps">
                                <div class="free-hunting-ticket"></div>
                                <div class="free-hunting-ticket-num">
                                    <span>x</span>3
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts">

export default {
    name: 'firstMatchDialog',
    props: {
        show: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            firstMatchDialogShow: this.show,
            timerList: [null, null, null, null, null, null],
            currentImageIndex: [0, 0, 0, 0, 0, 0], // 每个头像当前显示的图片索引
        };
    },
    watch: {
        show: {
            handler(newVal) {
                this.firstMatchDialogShow = newVal
            },
            immediate: true
        }
    },
    mounted() {
        this.startImageAnimation();
    },
    beforeUnmount() {
        this.stopImageAnimation();
    },
    methods: {
        close() {
            this.firstMatchDialogShow = false
            this.$emit('close')
            this.$emit('update:show', false)
        },
        getCurrentImage(index: number) {
            return this.currentImageIndex[index];
        },
        onAnimationIteration(index: number) {
            // 动画迭代时切换图片
            this.currentImageIndex[index] = (this.currentImageIndex[index] + 1) % 3;
        },
        startImageAnimation() {
            // 这个方法现在不需要做任何事情，因为使用CSS动画事件
        },
        stopImageAnimation() {
            // 清除所有定时器
            this.timerList.forEach((timer, index) => {
                if (timer) {
                    clearInterval(timer);
                    this.timerList[index] = null;
                }
            });
        }
    }
}
</script>
<style lang='scss' scoped>
.first-match-dialog {
    background: linear-gradient(180deg, #EBE5FF 0%, #FCC4F6 100%);
    border-radius: 20px 20px 0 0;
    padding: 30px 0 0;
    height: 440px;
    font-family: var(--var-font-family-urbanist);
    box-sizing: border-box;
    .close-dialog{
        position: absolute;
        width: 15px;
        height: 15px;
        top: 15px;
        right: 16px;
    }
    .first-match-dialog-content {
        height: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;

        &-title {
            font-size: 22px;
            font-weight: 900;
            color: #000;
            line-height: 26px;
            text-align: center;
        }

        &-desc {
            margin-top: 4px;
            padding: 0 64px;
            box-sizing: border-box;
            font-size: 14px;
            font-weight: 500;
            line-height: 17px;
            color: #404040;
            text-align: center;
        }

        .free-modal-content {
            width: 308px;
            height: 276px;
            position: relative;
            z-index: 5;

            .free-modal-content-inner {
                width: 308px;
                height: 308px;
                position: relative;
                margin-top: -20px;

                .free-modal-content-c {
                    width: 308px;
                    height: 308px;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    .free-modal-content-w {
                        position: absolute;
                        left: 0;
                        top: 0;
                        width: 100%;
                        height: 100%;
                        background-size: 100% 100%;
                        background-image: url('@/assets/home/<USER>');
                        opacity: 0;
                        animation: w-animation 3s infinite linear;

                        &.free-modal-content-w2 {
                            animation-delay: -1s;
                        }

                        &.free-modal-content-w3 {
                            animation-delay: -2s;
                        }
                    }

                    .free-modal-content-call {
                        width: 68px;
                        height: 68px;
                        background: linear-gradient(159deg, #27ff40 0%, #c4ff43 100%);
                        border-radius: 50%;
                        position: absolute;
                        top: 120px;
                        left: 120px;

                        &-icon {
                            width: 32px;
                            height: 32px;
                            background-size: 100% 100%;
                            background-image: url('@/assets/home/<USER>');
                            position: absolute;
                            left: 15px;
                            top: 20px;
                            animation: swing-call 1.5s 0s infinite;
                        }

                        &-text {
                            font-size: 13px;
                            color: #222c20;
                            font-family: var(--font-family-type1);
                            position: absolute;
                            right: 9px;
                            top: 18px;
                        }
                    }
                }

                .free-modal-content-anchor {
                    width: 308px;
                    height: 308px;
                    position: absolute;
                    top: 0;
                    left: 0;

                    .free-modal-content-anchor-item {
                        position: absolute;
                        width: 64px;
                        height: 64px;
                        animation: s-img 6s infinite linear;

                        &-img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                            border-radius: 50%
                        }

                        &:nth-of-type(1) {
                            left: 53px;
                            top: 37px;
                            width: 54px;
                            height: 54px;
                        }

                        &:nth-of-type(2) {
                            left: 200px;
                            top: 49px;
                            width: 54px;
                            height: 54px;
                            animation-delay: -1s;
                        }

                        &:nth-of-type(3) {
                            left: 7px;
                            top: 117px;
                            width: 48px;
                            height: 48px;
                            animation-delay: -1.5s;
                        }

                        &:nth-of-type(4) {
                            left: 252px;
                            top: 131px;
                            animation-delay: -2s;
                        }

                        &:nth-of-type(5) {
                            left: 33px;
                            top: 200px;
                            animation-delay: -2.5s;
                        }

                        &:nth-of-type(6) {
                            left: 237px;
                            top: 203px;
                            animation-delay: -0.5s;
                        }
                    }
                }
            }
        }

        .free-hunting-footer {
            z-index: 10;

            .free-hunting-btn {
                width: 210px;
                height: 52px;
                border-radius: 26px;
                background: linear-gradient(90deg, #ff8e2d 0%, #ff06ca 100%);
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                &-inner {
                    font-family: var(--font-family-type1);
                    font-size: 16px;
                    line-height: 18px;
                    text-align: center;
                    color: #fff;
                }

                &-ps {
                    font-family: var(--font-family-type3);
                    height: 18px;
                    text-align: center;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    justify-content: center;
                    font-size: 12px;
                    color: #fff;

                    .free-hunting-ticket {
                        width: 16px;
                        height: 13px;
                        background-size: 100% 100%;
                        background-image: url('@/assets/home/<USER>');
                        margin: 0 .8vw;
                        animation: swing-call 2s 0s infinite;
                    }
                }
            }
        }
    }

    @keyframes w-animation {
        0% {
            transform: scale(0.1);
            opacity: 1;
        }

        100% {
            transform: scale(1);
            opacity: 0;
        }
    }

    @keyframes swing-call {

        0% {
            transform: rotate(0);
        }

        30% {
            transform: rotate(0);
        }

        40% {
            transform: rotate(8deg);
        }

        50% {
            transform: rotate(-8deg) scale(1.15);
        }

        60% {
            transform: rotate(8deg) scale(1.15);
        }

        70% {
            transform: rotate(-8deg);
        }

        80% {
            transform: rotate(8deg);
        }

        90% {
            transform: rotate(-8deg);
        }

        100% {
            transform: rotate(0);
        }
    }

    @keyframes s-img {
        0% {
            transform: scale(.625);
            opacity: 0;
        }

        50% {
            transform: scale(1);
            opacity: 1;
        }

        100% {
            transform: scale(.625);
            opacity: 0;
        }
    }
}
</style>