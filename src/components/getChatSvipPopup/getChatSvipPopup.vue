<!-- 选择性别弹窗 -->
<template>
    <div>
        <var-popup position="bottom" v-model:show="showGetChatSvipPopup" @click-overlay="close" :safe-area="true"
            :default-style="false">
            <div class="get-chat-svip-popup">
                <div class="get-chat-svip-popup-title">
                    <div class="get-chat-svip-popup-title-box">
                        <img src="@/assets/common/vip.png" alt="">
                        <span class="title">Get SVIP</span>
                        <img src="@/assets/common/vip.png" alt="">
                    </div>

                </div>
                <div class="get-chat-svip-popup-content">
                    <Swiper ref="swiperRef" :loop="false" :autoplay="false" :pagination="false" :allowSlideNext="true"
                        :allowSlidePrev="true" @swiper="onSwiper" class="story-swiper">
                        <SwiperSlide v-for="(item, index) in swiperList" :key="index">
                            <div class="get-chat-svip-popup-content-item">
                                <div class="banner-item" :style="{ backgroundImage: `url(${item.img})` }">
                                    <div class="banner-item-desc">
                                        {{ item.desc }}
                                    </div>
                                </div>
                            </div>
                        </SwiperSlide>
                    </Swiper>
                    <!-- 自定义分页器 -->
                    <div class="swiper-pagination">
                        <img src="@/assets/memberShip/swiper-right.png" alt="" @click="swiperPrev">
                        <img src="@/assets/memberShip/swiper-left.png" alt="" @click="swiperNext">
                    </div>
                </div>
                <!--svip 权益说明-->
                <div class="svip-rights-desc">
                    <div class="svip-rights-desc-item" v-for="(item, index) in svipRightsDescList" :key="index">
                        <img :src="item.url" alt="">
                        <span>{{ item.title }}</span>
                    </div>
                </div>
                <div class="bottom-btn">
                    <div class="bottom-btn-item">
                        <div class="bottom-btn-item-left">
                            $19.99 /Mo
                        </div>
                        <div class="bottom-btn-item-right">
                            Get Now
                        </div>
                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts">
import { Swiper, SwiperSlide } from 'swiper/vue'
import 'swiper/css'
import 'swiper/css/autoplay'
import 'swiper/css/pagination'
export default {
    name: 'getChatSvipPopup',
    props: {
        show: {
            type: Boolean,
            default: false
        }
    },
    components: {
        Swiper,
        SwiperSlide
    },
    data() {
        return {
            svipRightsDescList: [
                {
                    title: 'Free coins',
                    url: new URL('@/assets/memberShip/free-coins.png', import.meta.url).href
                },
                {
                    title: 'Free Message',
                    url: new URL('@/assets/memberShip/free-messages.png', import.meta.url).href
                },
                {
                    title: 'Special Logo',
                    url: new URL('@/assets/memberShip/special-logo.png', import.meta.url).href
                },
                {
                    title: 'Svip Gifts',
                    url: new URL('@/assets/memberShip/svip-gIfts.png', import.meta.url).href
                },
                {
                    title: 'Top Ranking',
                    url: new URL('@/assets/memberShip/top-ranking.png', import.meta.url).href
                },

            ],
            swiperList: [
                {
                    desc: 'SVIP: Free messages Non-SVIP: 2 coins/msg',
                    img: 'https://iulia.iwlive.club/webapp/aso/pro/@1.0.6p/png/rights_w_02-4c72c2bf.png'
                },
                {
                    desc: ' Get 170 coins/month now!',
                    img: 'https://iulia.iwlive.club/webapp/aso/pro/@1.0.6p/png/rights_w_01-66b86846.png'
                },
                {
                    desc: 'dfsdafafsafas',
                    img: 'https://iulia.iwlive.club/webapp/aso/pro/@1.0.6p/png/rights_w_03-458e03a4.png'
                }

            ],
            showGetChatSvipPopup: this.show,
            swiperInstance: null as any
        };
    },
    watch: {
        show: {
            handler(newVal) {
                this.showGetChatSvipPopup = newVal
            },
            immediate: true
        }
    },
    mounted() {

    },
    methods: {
        close() {
            this.showGetChatSvipPopup = false
            this.$emit('close')
            this.$emit('update:show', false)
        },
        onSwiper(swiper: any) {
            this.swiperInstance = swiper;
        },
        swiperPrev() {
            if (this.swiperInstance) {
                this.swiperInstance.slidePrev();
            }
        },
        swiperNext() {
            if (this.swiperInstance) {
                this.swiperInstance.slideNext();
            }
        }
    }
}
</script>
<style lang='scss' scoped>
.get-chat-svip-popup {
    width: 100%;
    font-family: var(--font-family-urbanist);
    padding: 20px 0;
    background-color: #191716;

    .get-chat-svip-popup-title {
        display: flex;
        align-items: center;
        justify-content: center;

        &-box {
            width: max-content;
            padding: 4px 37px 7px;
            background: url('@/assets/memberShip/svip-title-bg.png') no-repeat center center;
            background-size: 100% 100%;
            box-sizing: border-box;
            display: flex;
            align-items: center;

            img {
                width: 20px;
                height: 20px;
            }

            .title {
                padding: 0 10px;
                font-size: 18px;
                line-height: 22px;
                font-weight: 700;
                font-style: italic;
                background: linear-gradient(90deg, #FFEFD4 0%, #FFF1DF 48.56%, #FDD4A1 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }
        }
    }

    .get-chat-svip-popup-content {
        width: 100%;
        height: 250px;
        position: relative;

        .swiper {
            width: 100%;
            height: 100%;

            .get-chat-svip-popup-content-item {
                width: 100%;
                height: 100%;

                .banner-item {
                    width: 100%;
                    height: 100%;
                    background-size: auto 100%;
                    background-repeat: no-repeat;
                    background-position: center center;
                    display: flex;
                    align-items: flex-end;
                    justify-content: center;
                    color: #fff;
                    font-size: 14px;
                    font-weight: 500;
                    line-height: 17px;
                }
            }
        }

        .swiper-pagination {
            position: absolute;
            width: 100%;
            height: 100%;
            padding: 0 20px;
            box-sizing: border-box;
            left: 0;
            top: 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            pointer-events: none;
            z-index: 10;

            img {
                width: 20px;
                cursor: pointer;
                pointer-events: auto;
                transition: opacity 0.3s ease;

                &:hover {
                    opacity: 0.7;
                }
            }
        }
    }

    .svip-rights-desc {
        margin-top: 30px;
        display: flex;
        align-items: center;
        gap: 10px;
        overflow-x: auto;
        padding-left: 20px;
        box-sizing: border-box;

        .svip-rights-desc-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            img {
                width: 36px;
                height: 36px;
                border-radius: 50%;
                background-color: #2d2b28;
            }

            span {
                margin-top: 11px;
                font-size: 12px;
                font-weight: 600;
                color: #fff;
            }
        }
    }

    .bottom-btn {
        margin-top: 20px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 23px;
        box-sizing: border-box;

        .bottom-btn-item {
            width: 100%;
            padding: 15px 20px;
            background: linear-gradient(163.19deg, #FBEECB 1.55%, #FBF5E5 49.7%, #F6CD96 78.14%, #EDBEC4 96.94%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .bottom-btn-item-left {
                font-size: 20px;
                font-weight: 700;
                color: #000;
            }

            .bottom-btn-item-right {
                padding: 6px 12px;
                font-size: 15px;
                font-weight: 700;
                line-height: 18px;
                color: #FFE39F;
                background: linear-gradient(90deg, #574F3A 0%, #15120A 100%);
                border-radius: 20px;
            }
        }
    }
}
</style>