<!-- 需要绑定邮箱提示 -->
<template>
    <div class="coins-not-enough-popup">
        <var-popup position="bottom" v-model:show="showBindEmailTip" :safe-area="true" :default-style="false"
            @click-overlay="close">
            <div class="coins-not-enough-dialog">
                <img src="@/assets/recharge/purchase-popup-bg.png" alt="" class="purchase-bg" @click="clickShowDiscount">
                <div class="coins-not-enough-dialog-content">
                    <div class="coins-not-enough-dialog-content-title">
                        <img src="@/assets/common/coins.png" alt="" class="coins-icon">
                        <span>0</span>
                    </div>
                    <!--下面的充值List-->
                    <div class="recharge-svip-list">
                        <div class="recharge-svip-list-item" v-for="item in rechargeList" :key="item.id"
                            :class="{ 'recharge-svip-list-item-special': item.isSpecial }">
                            <div class="recharge-svip-list-item-top">
                                <img src="@/assets/common/coins.png" alt="" class="recharge-svip-list-item-top-img">
                                <span class="recharge-svip-list-item-top-text">{{ item.coins }}</span>
                            </div>
                            <div class="recharge-svip-list-item-bottom">
                                {{ item.price }}
                            </div>
                            <div class="recharge-svip-list-item-special-offer" v-if="item.isSpecial">
                                <div class="recharge-svip-list-item-special-offer-left">
                                    <span class="recharge-svip-list-item-special-offer-left-text">{{ item.speicalOffer ?
                                        item.speicalOffer : 'Special offer' }}</span>
                                </div>
                                <div class="recharge-svip-list-item-special-offer-right">
                                    <span class="recharge-svip-list-item-special-offer-right-text">20:20</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--客服-->
                    <div class="connect-us">
                        <img src="@/assets/recharge/connect-us.png" alt="" class="connect-us-img">
                    </div>
                </div>
            </div>
        </var-popup>
    </div>
</template>

<script lang="ts">

export default {
    name: 'coinsNotEnoughPopup',
    props: {
        show: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            rechargeList: [
                {
                    id: 1,
                    coins: '170',
                    price: '$19',
                    isSpecial: true,
                    speicalOffer: '+100 coins'
                },
                {
                    id: 2,
                    coins: '170',
                    price: '$19',
                    isSpecial: true,
                    speicalOffer: ''
                },
                {
                    id: 3,
                    coins: '170',
                    price: '$19',
                    isSpecial: false,
                },
                {
                    id: 4,
                    coins: '170',
                    price: '$19',
                    isSpecial: false,
                },
                {
                    id: 5,
                    coins: '170',
                    price: '$19',
                    isSpecial: false,
                },
            ],
            showBindEmailTip: this.show
        };
    },
    watch: {
        show: {
            handler(newVal) {
                this.showBindEmailTip = newVal
            },
            immediate: true
        }
    },
    mounted() {

    },
    methods: {
        close() {
            this.showBindEmailTip = false
            this.$emit('close')
            this.$emit('update:show', false)
        },
        confirm() {

            this.$emit('confirm')
            this.close()
        },
        clickShowDiscount(){
            this.close()
            this.$emit('showDiscount')
        }
    }
}
</script>
<style lang='scss' scoped>
.coins-not-enough-dialog {
    display: flex;
    justify-content: center;
    font-family: var(--var-font-family-type3);
    padding: 0;
    border-radius: 16px 16px 0 0;
    background-color: #0A0A1B;
    display: flex;
    flex-direction: column;
    align-items: center;
    .purchase-bg {
        width: 100%;
        height: 60px;
    }
    .coins-not-enough-dialog-content {
        width: 100%;
        padding: 0 15px 50px;
        box-sizing: border-box;
        position: relative;
        .coins-not-enough-dialog-content-title{
            margin-top: 20px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap:4px;
            .coins-icon{
                width: 16px;
                height: 16px;

            }  
            span{
               font-size: 18px; 
               color: #fff;
               font-weight: 600;
               line-height: 21px;
            }          
        }
        .recharge-svip-list {
            margin-top: 22px;
            width: 100%;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 5px;
            &-item {
                width: 100%;
                height: 110px;
                background-color: #1E1D38;
                border-radius: 10px;
                border: 1px solid #1E1D38;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                position: relative;

                &-top {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;

                    &-img {
                        width: 16px;
                        height: 16px;
                        margin-bottom: 4px;
                    }

                    &-text {
                        color: #fff;
                        font-size: 16px;
                        font-weight: 700;
                        line-height: 19px;
                    }
                }

                &-bottom {
                    width: 100%;
                    height: 24px;
                    text-align: center;
                    line-height: 24px;
                    border-radius: 0 0 10px 10px;
                    background-color: rgba($color: #fff, $alpha: .1);
                    color: #fff;
                    font-size: 14px;
                    font-weight: 500;
                }
            }

            &-item-special {
                border-color: #FE3555;

                .recharge-svip-list-item-special-offer {
                    position: absolute;
                    top: 0;
                    left: -1px;
                    width: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding-inline-end: 4px;

                    &-left {
                        border-radius: 10px 10px 10px 0;
                        background-color: #FE3555;
                        padding: 2px 5px;

                        &-text {
                            display: block;
                            font-size: 10px;
                            line-height: 12px;
                            font-weight: 700;
                            color: #fff;
                        }
                    }

                    &-right {
                        &-text {
                            display: block;
                            color: #FE3555;
                            font-size: 10px;
                            line-height: 12px;
                            font-weight: 700;
                        }
                    }
                }
            }
        }
        .connect-us{
            position: absolute;
            bottom: 40px;
            right: 15px;
            width: 40px;
            height: 40px;
            background-color: rgba($color: #fff, $alpha: .1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            .connect-us-img{
                width: 30px;
                height: 30px;
            }
        }
    }
}
</style>