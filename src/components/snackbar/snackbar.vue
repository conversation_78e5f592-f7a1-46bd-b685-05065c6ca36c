<!--  -->
<template>
    <var-snackbar v-model:show="show" :duration="2000" content-class="message-success" >
        <div class="message-success">
            <img src="@/assets/common/message-success.png" alt="">
            <div class="message-success-right">
                <div class="title">
                    <slot name="title"></slot>
                </div>
                <div class="desc">
                    <slot name="desc"></slot>
                </div>

            </div>
        </div>
    </var-snackbar>
</template>

<script lang="ts" setup name="Snackbar">
import { watch } from 'vue'
import { ref } from 'vue'
const emit = defineEmits(['close'])
const props = defineProps({
    show: {
        type: Boolean,
        default: false
    },
    type: {
        type: String,
        default: 'success'
    }
})
const show = ref(false)
watch(() => props.show, (newVal) => {
    show.value = newVal
}, {
    immediate: true,
    deep: true
})
watch(show, (newVal) => {
    if (!newVal) {
       emit('close')
    }
})
</script>
<style lang="scss" scoped>
.message-success {
    display: flex;
    align-items: center;
    gap: 10px;

    img {
        width: 40px;
        height: 40px;
    }

    .message-success-right {
        font-family: var(--font-family-urbanist);
        .title {
            font-size: 18px;
            font-weight: 700;
            line-height: 22px;
            color: #fff;
        }
        .desc{
            margin-top: 4px;
            font-size:14px;
            font-weight: 500;
            line-height: 17px;
            color: rgba($color: #fff, $alpha: .5);
        }
    }
}
</style>