<!--  -->
<template>
    <div class="common-nav">
        <div class="common-nav-left">
            <img src="@/assets/common/go-back.png" alt="" @click="router.back()">
        </div>
        <div class="common-nav-title">
            {{ title }}
        </div>
        <div class="common-nav-right">
            <slot name="right"></slot>
        </div>
    </div>
</template>

<script lang="ts" setup name="headerNav">
import { ref } from 'vue'

import { useRouter } from 'vue-router'
const router = useRouter()
const props = defineProps({
    title: {
        type: String,
        default: ''
    }
})
</script>
<style lang="scss" scoped>
.common-nav {
    width: 100%;
    height: 44px;
    padding: 0 15px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-left {
        img {
            width: 24px;
            height: 24px;
        }
    }

    &-title {
        font-size: 18px;
        font-weight: 700;
        line-height: 22px;
        color: #fff;
    }

    &-right {
        font-size: 16px;
        font-weight: 700;
        color: $common-color;
    }
}
</style>