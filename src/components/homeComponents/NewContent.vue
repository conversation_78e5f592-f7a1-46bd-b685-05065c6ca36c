<template>
    <div class="hot-content">
        <!-- 骨架屏 -->
        <div v-if="isLoading" class="skeleton-container">
            <!-- 特色直播骨架屏 -->
            <div class="hot-card">
                <div class="skeleton-card" v-for="i in 2" :key="`featured-${i}`">
                    <div class="skeleton-image"></div>
                    <div class="skeleton-header">
                        <div class="skeleton-status"></div>
                        <div class="skeleton-user-count"></div>
                    </div>
                    <div class="skeleton-bottom">
                        <div class="skeleton-hot-badge"></div>
                        <div class="skeleton-text">
                            <div class="skeleton-name"></div>
                            <div class="skeleton-title"></div>
                        </div>
                    </div>
                    <div class="skeleton-avatars">
                        <div class="skeleton-avatar" v-for="j in 5" :key="j"></div>
                    </div>
                </div>
            </div>

            <!-- 轮播图骨架屏 -->
            <div class="skeleton-swiper"></div>

            <!-- 直播列表骨架屏 -->
            <div class="hot-card">
                <div class="skeleton-card" v-for="i in 6" :key="`live-${i}`">
                    <div class="skeleton-image"></div>
                    <div class="skeleton-header">
                        <div class="skeleton-status"></div>
                        <div class="skeleton-user-count"></div>
                    </div>
                    <div class="skeleton-bottom">
                        <div class="skeleton-hot-badge"></div>
                        <div class="skeleton-text">
                            <div class="skeleton-name"></div>
                            <div class="skeleton-title"></div>
                        </div>
                    </div>
                    <div class="skeleton-avatars">
                        <div class="skeleton-avatar" v-for="j in 5" :key="j"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实际内容 -->
        <div v-else>
            <!-- 特色直播区域 -->
            <div class="hot-card">
                <div class="featured-card">
                    <img :src="getImageSrc('../../assets/home/<USER>')" alt="" class="featured-card-img"
                        ref="imgRef1">
                    <div class="featured-card-header">
                        <div class="featured-card-header-left">
                            <img src="@/assets/home/<USER>" alt="" class="new-img">
                        </div>
                        <div class="featured-card-header-right">
                            <img src="@/assets/home/<USER>" alt="" class="like-img">
                            <span class="like-text">{{ Math.floor(Math.random() * 10000) }}</span>
                        </div>
                    </div>
                </div>

                <div class="featured-card">
                    <img :src="getImageSrc('../../assets/home/<USER>')" alt="" class="featured-card-img"
                        ref="imgRef2">
                    <div class="featured-card-header">
                        <div class="featured-card-header-left">
                            <img src="@/assets/home/<USER>" alt="" class="new-img">
                        </div>
                        <div class="featured-card-header-right">
                            <img src="@/assets/home/<USER>" alt="" class="like-img">
                            <span class="like-text">{{ Math.floor(Math.random() * 10000) }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <!--中间轮播图-->
            <var-swipe class="swipe-example" :loop="true" :autoplay="3000" :indicator="false" @click.stop
                @touchstart.stop @touchmove.stop @touchend.stop>
                <var-swipe-item v-for="(item, index) in swiperList" :key="index">
                    <img class="swipe-example-image" :src="item.img" :alt="`Slide ${index + 1}`">
                </var-swipe-item>
                <template #indicator="{ index, length, to }">
                    <div class="swipe-example-indicators">
                        <div class="swipe-example-indicator" v-for="(l, idx) in length" :key="idx"
                            :class="{ 'swipe-example-active-indicator': idx === index }" @click.stop="to(idx)">
                        </div>
                    </div>
                </template>
            </var-swipe>
            <div class="hot-card">
                <div class="featured-card" v-for="(item, index) in hotList" :key="item.id">
                    <img :src="getImageSrc(item.img, `live-${item.id}`)" alt="" class="featured-card-img"
                        :ref="el => setImageRef(el, `live-${item.id}`)">
                    <div class="featured-card-header">
                        <div class="featured-card-header-left">
                            <img src="@/assets/home/<USER>" alt="" class="new-img" v-if="item.isNew">
                            <img src="@/assets/home/<USER>" alt="" class="hot-img" v-else-if="item.isHot">
                        </div>
                        <div class="featured-card-header-right">
                            <img src="@/assets/home/<USER>" alt="" class="like-img">
                            <span class="like-text">{{ Math.floor(Math.random() * 10000) }}</span>
                        </div>
                    </div>
                    <!---下面部分-->
                    <div class="featured-card-bottom">
                        <div class="featured-card-bottom-left">
                            <div class="featured-card-bottom-left-age">
                                <img src="@/assets/home/<USER>" alt="">
                                <span class="featured-card-bottom-left-age-text">{{ item.age }}</span>
                            </div>
                            <div class="featured-card-bottom-left-title">
                                <div class="featured-card-bottom-left-isonline" v-if="item.isOnline">

                                </div>
                                <span class="featured-card-bottom-left-title-text">{{ item.name }}</span>
                            </div>
                        </div>
                        <img src="@/assets/home/<USER>" alt="" class="featured-card-bottom-right">
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup name="newContent">
// 组件逻辑可以在这里添加
import { ref, onMounted, PropType, nextTick, ComponentPublicInstance, watch } from 'vue'
import img1 from '@/assets/1.png'
import img2 from '@/assets/2.gif'
import img3 from '@/assets/3.png'
import img4 from '@/assets/4.gif'
import positionImg from '@/assets/common/position.png'

const { hotList } = defineProps({
    hotList: {
        type: Array as PropType<any[]>,
        default: () => []
    }
})

const swiperList = ref([
    {
        img: img1,
    },
    {
        img: img2,
    },
    {
        img: img3,
    },
    {
        img: img4,
    }
])

// 加载状态
const isLoading = ref(true)

// 图片可见性状态管理
const imageVisibility = ref<Record<string, boolean>>({})
const imageRefs = ref<Record<string, HTMLElement>>({})
const imageLoaded = ref<Record<string, boolean>>({}) // 记录图片是否已经加载过

// 设置图片引用
const setImageRef = (el: Element | ComponentPublicInstance | null, key: string) => {
    if (el && el instanceof HTMLElement) {
        imageRefs.value[key] = el
        // 立即设置观察器
        setupObserver(el, key)
    }
}

// 设置观察器
const setupObserver = (element: HTMLElement, key: string) => {
    if (!observer) {
        observer = new IntersectionObserver((entries) => {
            entries.forEach((entry) => {
                const key = entry.target.getAttribute('data-image-key')
                if (key) {
                    imageVisibility.value[key] = entry.isIntersecting

                    // 如果图片第一次出现在视图中，标记为已加载并停止观察
                    if (entry.isIntersecting && !imageLoaded.value[key]) {
                        imageLoaded.value[key] = true
                        observer?.unobserve(entry.target)
                        console.log(`图片 ${key} 已加载，停止观察`)
                    }
                }
            })
        }, {
            threshold: 0.1, // 当10%的图片可见时触发
            rootMargin: '50px' // 提前50px开始观察
        })
    }
    element.setAttribute('data-image-key', key)
    observer.observe(element)
}

// 获取图片源
const getImageSrc = (originalSrc: string, key?: string) => {
    if (key && imageVisibility.value[key] === false && !imageLoaded.value[key]) {
        return positionImg
    }
    if (!originalSrc.includes('http')) {
        return new URL(originalSrc, import.meta.url).href
    }
    return originalSrc
}

// 创建 Intersection Observer
let observer: IntersectionObserver | null = null

// 监听 liveList 变化
watch(() => hotList, (newList) => {
    if (newList && newList.length > 0) {
        // 延迟隐藏骨架屏，模拟加载过程
        setTimeout(() => {
            isLoading.value = false
        }, 1500)
    }
}, { immediate: true })

onMounted(async () => {
    console.log('LiveContent mounted, swiperList:', swiperList.value)

    // 如果没有数据，显示骨架屏
    if (!hotList || hotList.length === 0) {
        isLoading.value = true
    } else {
        // 有数据时延迟隐藏骨架屏
        setTimeout(() => {
            isLoading.value = false
        }, 1500)
    }
})

// 组件卸载时清理 observer
import { onUnmounted } from 'vue'
onUnmounted(() => {
    if (observer) {
        observer.disconnect()
    }
})

</script>

<style lang="scss" scoped>
// Live 样式
.hot-content {
    .hot-card {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 9px;
        margin-bottom: 10px;

        .featured-card {
            width: 100%;
            height: 252px;
            overflow: hidden;
            position: relative;

            &-img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 12px;
            }

            &-header {
                position: absolute;
                top: 8px;
                width: 100%;
                padding: 0 8px 0 6px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                z-index: 10;

                &-left {
                    height: 12px;

                    img {
                        height: 100%;
                    }

                }

                &-right {
                    height: 20px;
                    display: flex;
                    align-items: center;
                    gap: 2vpx;
                    padding: 0 4px;
                    background: #00000080;
                    border-radius: 10px;

                    img {
                        width: 16px;
                        height: 16px;
                    }

                    span {
                        font-size: 10px;
                        color: #fff;
                        font-weight: 700;
                    }
                }
            }

            &-bottom {
                position: absolute;
                bottom: 0;
                width: 100%;
                padding: 0 8px 8px 6px;
                border-radius: 0 0 12px 12px;
                background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 100%);
                display: flex;
                align-items: center;
                justify-content: space-between;

                &-left {
                    &-age {
                        width: max-content;
                        height: 12px;
                        padding: 0 4px;
                        display: flex;
                        align-items: center;
                        gap: 2px;
                        background: $common-color;
                        border-radius: 6px;
                        margin-bottom: 4px;

                        img {
                            width: 10px;
                        }

                        &-text {
                            font-size: 10px;
                            color: #fff;
                            font-weight: 800;
                        }
                    }

                    &-title {
                        display: flex;
                        align-items: center;
                        gap: 4px;

                        &-text {
                            font-size: 16px;
                            line-height: 19px;
                            color: #fff;
                            font-weight: 700;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }

                        &-isonline {
                            width: 8px;
                            height: 8px;
                            background: #12F38C;
                            border-radius: 50%;
                        }
                    }
                }

                &-right {
                    width: 48px;
                    height: 48px;
                }
            }
        }
    }
}

// 骨架屏样式
.skeleton-container {
    .hot-card {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 9px;
        margin-bottom: 10px;

        .skeleton-card {
            width: 100%;
            height: 200px;
            overflow: hidden;
            position: relative;
            background: #222341;
            border-radius: 12px;
            animation: skeleton-pulse 1.5s ease-in-out infinite;

            .skeleton-image {
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, #30315e 25%, #453da4 50%, #30315e 75%);
                background-size: 200% 100%;
                animation: skeleton-shimmer 2s infinite;
                border-radius: 12px;
            }

            .skeleton-header {
                position: absolute;
                top: 6px;
                width: 100%;
                padding: 0 8px 0 6px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                z-index: 10;

                .skeleton-status {
                    width: 40px;
                    height: 20px;
                    background: linear-gradient(90deg, #30315e 25%, #453da4 50%, #30315e 75%);
                    background-size: 200% 100%;
                    animation: skeleton-shimmer 2s infinite;
                    border-radius: 4px;
                }

                .skeleton-user-count {
                    width: 30px;
                    height: 12px;
                    background: linear-gradient(90deg, #30315e 25%, #453da4 50%, #30315e 75%);
                    background-size: 200% 100%;
                    animation: skeleton-shimmer 2s infinite;
                    border-radius: 2px;
                }
            }

            .skeleton-bottom {
                position: absolute;
                bottom: 0;
                width: 100%;

                .skeleton-hot-badge {
                    width: 20px;
                    height: 13px;
                    background: linear-gradient(90deg, #30315e 25%, #453da4 50%, #30315e 75%);
                    background-size: 200% 100%;
                    animation: skeleton-shimmer 2s infinite;
                    border-radius: 2px;
                    margin-bottom: 2px;
                    margin-left: 8px;
                }

                .skeleton-text {
                    width: 100%;
                    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 100%);
                    padding: 0px 8px 8px;

                    .skeleton-name {
                        width: 60%;
                        height: 16px;
                        background: linear-gradient(90deg, #30315e 25%, #453da4 50%, #30315e 75%);
                        background-size: 200% 100%;
                        animation: skeleton-shimmer 2s infinite;
                        border-radius: 2px;
                        margin-bottom: 4px;
                    }

                    .skeleton-title {
                        width: 80%;
                        height: 14px;
                        background: linear-gradient(90deg, #30315e 25%, #453da4 50%, #30315e 75%);
                        background-size: 200% 100%;
                        animation: skeleton-shimmer 2s infinite;
                        border-radius: 2px;
                    }
                }
            }

            .skeleton-avatars {
                position: absolute;
                bottom: 51px;
                width: 100%;
                right: 10px;
                display: flex;
                align-items: center;
                justify-content: flex-end;
                gap: 2px;

                .skeleton-avatar {
                    width: 20px;
                    height: 20px;
                    background: linear-gradient(90deg, #30315e 25%, #453da4 50%, #30315e 75%);
                    background-size: 200% 100%;
                    animation: skeleton-shimmer 2s infinite;
                    border-radius: 50%;
                }
            }
        }
    }

    .skeleton-swiper {
        height: 115px;
        margin-bottom: 20px;
        border-radius: 12px;
        background: linear-gradient(90deg, #30315e 25%, #453da4 50%, #30315e 75%);
        background-size: 200% 100%;
        animation: skeleton-shimmer 2s infinite;
    }
}

// 骨架屏动画
@keyframes skeleton-pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }
}

@keyframes skeleton-shimmer {
    0% {
        background-position: -200% 0;
    }

    100% {
        background-position: 200% 0;
    }
}

// 响应式设计
@media (max-width: 768px) {
    .hot-card {
        grid-template-columns: 1fr;
    }

    .small-live-cards {
        grid-template-columns: 1fr;
    }

    .match-cards {
        grid-template-columns: 1fr;
    }
}

.swipe-example {
    height: 115px;
    margin-bottom: 20px;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    /* 防止触摸事件冒泡到父元素 */
    touch-action: pan-y;

    :deep(.var-swipe) {
        height: 100%;
        /* 确保轮播容器能正确处理触摸事件 */
        touch-action: pan-x;
    }

    :deep(.var-swipe-item) {
        width: 100%;
        height: 115px;

        .swipe-example-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            /* 防止图片拖拽 */
            pointer-events: none;
            user-select: none;
        }
    }

    :deep(.swipe-example-indicators) {
        position: absolute;
        bottom: 10px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 6px;
        z-index: 10;
        /* 确保指示器可以点击 */
        pointer-events: auto;
    }

    :deep(.swipe-example-indicator) {
        width: 16px;
        height: 4px;
        border-radius: 2px;
        background: rgba(255, 255, 255, 0.5);
        cursor: pointer;
        transition: all 0.3s ease;

        &.swipe-example-active-indicator {
            background: #fff;
            transform: scale(1.2);
        }
    }
}
</style>