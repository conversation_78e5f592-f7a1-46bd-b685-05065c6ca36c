<template>
    <div class="verification-code-input" :class="[`size-${size}`, { disabled }]">
        <div class="code-input-container">
            <div v-for="(digit, index) in digits" :key="index" class="code-input-item" :class="{
                'active': index === currentIndex,
                'filled': digit !== '',
                'error': hasError
            }" @click="focusInput">
                <span v-if="digit !== ''" class="digit">{{ digit }}</span>
                <span v-else-if="index === currentIndex" class="cursor">|</span>
            </div>
        </div>

        <!-- 隐藏的输入框用于接收键盘输入 -->
        <input ref="hiddenInput" v-model="inputValue" type="text" :maxlength="length" class="hidden-input"
            @input="handleInput" @keydown="handleKeydown" @paste="handlePaste" @focus="handleFocus"
            @blur="handleBlur" />
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch, nextTick } from 'vue'

export default defineComponent({
    name: 'VerificationCodeInput',
    props: {
        // 验证码长度
        length: {
            type: Number,
            default: 6
        },
        // 是否只允许数字
        numbersOnly: {
            type: Boolean,
            default: true
        },
        // 是否自动聚焦
        autoFocus: {
            type: Boolean,
            default: true
        },
        // 错误状态
        error: {
            type: Boolean,
            default: false
        },
        // 禁用状态
        disabled: {
            type: Boolean,
            default: false
        },
        // 输入框大小
        size: {
            type: String,
            default: 'medium', // small, medium, large
            validator: (value: string) => ['small', 'medium', 'large'].includes(value)
        },
        // 模型值
        modelValue: {
            type: String,
            default: ''
        }
    },
    emits: ['update:modelValue', 'complete', 'change'],
    setup(props, { emit }) {
        const hiddenInput = ref<HTMLInputElement>()
        const inputValue = ref(props.modelValue)
        const currentIndex = ref(0)
        const hasError = ref(false)

        // 计算每个位置的数字
        const digits = computed(() => {
            const result = new Array(props.length).fill('')
            const valueArray = inputValue.value.split('')
            valueArray.forEach((char, index) => {
                if (index < props.length) {
                    result[index] = char
                }
            })
            return result
        })

        // 处理输入
        const handleInput = (event: Event) => {
            if (props.disabled) return

            const target = event.target as HTMLInputElement
            let value = target.value

            // 如果只允许数字，过滤非数字字符
            if (props.numbersOnly) {
                value = value.replace(/\D/g, '')
            }

            // 限制长度
            if (value.length > props.length) {
                value = value.slice(0, props.length)
            }

            inputValue.value = value
            currentIndex.value = Math.min(value.length, props.length - 1)

            emit('update:modelValue', value)
            emit('change', value)

            // 如果输入完成，触发完成事件
            if (value.length === props.length) {
                emit('complete', value)
            }
        }

        // 处理键盘事件
        const handleKeydown = (event: KeyboardEvent) => {
            if (props.disabled) return

            if (event.key === 'Backspace') {
                if (inputValue.value.length > 0) {
                    inputValue.value = inputValue.value.slice(0, -1)
                    currentIndex.value = Math.max(0, inputValue.value.length - 1)
                    emit('update:modelValue', inputValue.value)
                    emit('change', inputValue.value)
                }
            }
        }

        // 处理粘贴事件
        const handlePaste = (event: ClipboardEvent) => {
            if (props.disabled) return

            event.preventDefault()
            const pastedData = event.clipboardData?.getData('text') || ''
            let value = pastedData

            if (props.numbersOnly) {
                value = value.replace(/\D/g, '')
            }

            if (value.length > props.length) {
                value = value.slice(0, props.length)
            }

            inputValue.value = value
            currentIndex.value = Math.min(value.length, props.length - 1)

            emit('update:modelValue', value)
            emit('change', value)

            if (value.length === props.length) {
                emit('complete', value)
            }
        }

        // 处理聚焦
        const handleFocus = () => {
            if (props.disabled) return
            currentIndex.value = Math.min(inputValue.value.length, props.length - 1)
        }

        // 处理失焦
        const handleBlur = () => {
            currentIndex.value = -1
        }

        // 聚焦输入框
        const focusInput = () => {
            if (props.disabled) return
            hiddenInput.value?.focus()
        }

        // 清空输入
        const clear = () => {
            inputValue.value = ''
            currentIndex.value = 0
            emit('update:modelValue', '')
            emit('change', '')
        }

        // 设置值
        const setValue = (value: string) => {
            let newValue = value
            if (props.numbersOnly) {
                newValue = value.replace(/\D/g, '')
            }
            if (newValue.length > props.length) {
                newValue = newValue.slice(0, props.length)
            }
            inputValue.value = newValue
            currentIndex.value = Math.min(newValue.length, props.length - 1)
            emit('update:modelValue', newValue)
            emit('change', newValue)
        }

        // 监听错误状态
        watch(() => props.error, (newVal) => {
            hasError.value = newVal
        })

        // 监听模型值变化
        watch(() => props.modelValue, (newVal) => {
            inputValue.value = newVal
            currentIndex.value = Math.min(newVal.length, props.length - 1)
        })

        // 自动聚焦
        watch(() => props.autoFocus, (newVal) => {
            if (newVal && !props.disabled) {
                nextTick(() => {
                    hiddenInput.value?.focus()
                })
            }
        }, { immediate: true })

        // 暴露方法给父组件
        return {
            hiddenInput,
            inputValue,
            currentIndex,
            hasError,
            digits,
            handleInput,
            handleKeydown,
            handlePaste,
            handleFocus,
            handleBlur,
            focusInput,
            clear,
            setValue
        }
    }
})
</script>

<style lang="scss" scoped>
.verification-code-input {
    width: 100%;
    display: inline-block;

    .code-input-container {
        display: flex;
        gap: 9px;
        align-items: center;


        .code-input-item {
            flex: 1;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 50px;
            border-radius: 10px;
            background: #1E1D38;
            cursor: text;
            transition: all 0.2s ease;
           
            &.active {
                border-color: #007bff;
                box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
            }
            .digit {
                font-size: 18px;
                font-weight: 700;
                color: #fff;
                line-height: 1;
            }

            .cursor {
                font-size: 20px;
                color: #007bff;
                animation: blink 1s infinite;
            }
        }
    }

    .hidden-input {
        position: absolute;
        left: -9999px;
        width: 1px;
        height: 1px;
        opacity: 0;
        pointer-events: none;
    }
}

// 尺寸变体
.verification-code-input {
    &.size-small .code-input-item {
        width: 40px;
        height: 48px;

        .digit,
        .cursor {
            font-size: 16px;
        }
    }

    &.size-large .code-input-item {
        width: 56px;
        height: 64px;

        .digit,
        .cursor {
            font-size: 24px;
        }
    }
}

// 光标闪烁动画
@keyframes blink {

    0%,
    50% {
        opacity: 1;
    }

    51%,
    100% {
        opacity: 0;
    }
}

// 禁用状态
.verification-code-input.disabled {
    .code-input-item {
        background: #f5f5f5;
        border-color: #ddd;
        cursor: not-allowed;

        .digit {
            color: #999;
        }
    }
}
</style>